# Sistem Tempahan Bilik Mesyuarat - Bilik Baru

## Pengenalan
Sistem ini telah dikemas kini dengan **48 bilik mesyuarat baru** yang dibahagikan kepada 8 bahagian berbeza untuk memenuhi keperluan organisasi yang semakin berkembang.

## Senarai Bilik Mesyuarat Baru

### 🏢 Bahagian 1: Pentadbiran & Kesihatan Awam (5 bilik baru)
- **Bilik Mesyuarat Pentadbiran A** (15 orang)
- **Bilik Mesyuarat Pentadbiran B** (20 orang)
- **Bilik Perbincangan Kewangan** (12 orang)
- **Bilik Mesyuarat Sumber Manusia** (18 orang)
- **Bilik Mesyuarat Audit** (10 orang)

### 🏥 Bahagian 2: Perubatan (7 bilik baru)
- **Bilik Mesyuarat Pakar A** (25 orang)
- **Bilik Mesyuarat Pakar B** (30 orang)
- **Bilik Perbincangan Klinikal** (15 orang)
- **Bilik Mesyuarat Jururawat** (20 orang)
- **Bilik Mesyuarat Pemulihan** (12 orang)
- **Bilik Mesyuarat Radiologi** (18 orang)
- **Bilik Mesyuarat Makmal** (16 orang)

### 👔 Bahagian 3: Pengurusan (6 bilik baru)
- **Bilik Mesyuarat Eksekutif A** (40 orang)
- **Bilik Mesyuarat Eksekutif B** (35 orang)
- **Bilik Mesyuarat Strategik** (25 orang)
- **Bilik Perbincangan Dasar** (20 orang)
- **Bilik Mesyuarat Kualiti** (15 orang)
- **Bilik Mesyuarat Projek** (18 orang)

### 🦷 Bahagian 4: Pergigian (5 bilik baru)
- **Bilik Mesyuarat Pergigian A** (20 orang)
- **Bilik Mesyuarat Pergigian B** (15 orang)
- **Bilik Perbincangan Ortodontik** (12 orang)
- **Bilik Mesyuarat Oral Surgery** (10 orang)
- **Bilik Mesyuarat Periodontal** (14 orang)

### 💊 Bahagian 5: Farmasi (5 bilik baru)
- **Bilik Mesyuarat Farmasi A** (25 orang)
- **Bilik Mesyuarat Farmasi B** (20 orang)
- **Bilik Perbincangan Klinikal Farmasi** (15 orang)
- **Bilik Mesyuarat Penyelidikan Ubat** (12 orang)
- **Bilik Mesyuarat Kawalan Kualiti** (18 orang)

### 💻 Bahagian 6: Teknologi Maklumat dan Komunikasi (5 bilik baru)
- **Bilik Mesyuarat ICT Utama** (30 orang)
- **Bilik Mesyuarat Sistem** (20 orang)
- **Bilik Perbincangan Teknikal** (15 orang)
- **Bilik Mesyuarat Keselamatan Siber** (12 orang)
- **Bilik Mesyuarat Projek IT** (18 orang)

### 🔬 Bahagian 7: Penyelidikan dan Pembangunan (5 bilik baru)
- **Bilik Mesyuarat Penyelidikan A** (25 orang)
- **Bilik Mesyuarat Penyelidikan B** (20 orang)
- **Bilik Perbincangan Kajian** (15 orang)
- **Bilik Mesyuarat Etika** (12 orang)
- **Bilik Mesyuarat Inovasi** (18 orang)

### 📚 Bahagian 8: Latihan dan Pembangunan (5 bilik baru)
- **Bilik Mesyuarat Latihan A** (40 orang)
- **Bilik Mesyuarat Latihan B** (35 orang)
- **Bilik Perbincangan Kurikulum** (20 orang)
- **Bilik Mesyuarat Penilaian** (15 orang)
- **Bilik Mesyuarat Pembangunan Staf** (25 orang)

### 🌟 Bilik Mesyuarat Khas (5 bilik)
- **Bilik Mesyuarat VIP** (15 orang) - Bahagian Pengurusan
- **Bilik Mesyuarat Antarabangsa** (50 orang) - Bahagian Pengurusan
- **Bilik Mesyuarat Video Conference** (30 orang) - Bahagian ICT
- **Bilik Mesyuarat Hybrid** (25 orang) - Bahagian ICT
- **Bilik Mesyuarat Krisis** (20 orang) - Bahagian Pentadbiran

## Cara Menggunakan Sistem

### 1. Memasang Data Bilik Baru
```sql
-- Jalankan fail SQL untuk menambah bilik baru
mysql -u root -p nama_database < bilik_mesyuarat_baru.sql
```

### 2. Menggunakan Interface Web
1. **Lihat Senarai Bilik**: Akses `senarai_bilik_baru.php`
2. **Tempah Bilik**: Akses `tempah_bilik_baru.php`
3. **Tapis mengikut Bahagian**: Gunakan dropdown untuk menapis bilik

### 3. Ciri-ciri Sistem
- ✅ **Paparan Grid**: Bilik dipaparkan dalam format grid yang menarik
- ✅ **Penapis Bahagian**: Boleh menapis bilik mengikut bahagian
- ✅ **Maklumat Terperinci**: Setiap bilik menunjukkan kapasiti dan bahagian
- ✅ **Tempahan Mudah**: Borang tempahan yang lengkap dan mudah digunakan
- ✅ **Validasi**: Sistem memeriksa ketersediaan bilik sebelum tempahan
- ✅ **Responsive Design**: Sesuai untuk desktop dan mobile

## Struktur Pangkalan Data

### Jadual `tbilik_mesyuarat`
```sql
CREATE TABLE `tbilik_mesyuarat` (
  `id` int(6) NOT NULL,
  `nama_bilik_mesyuarat` varchar(100) DEFAULT NULL,
  `kapasiti` int(3) DEFAULT NULL,
  `bahagian` int(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### Jadual `tbilik_penyelaras` (Opsional)
```sql
CREATE TABLE `tbilik_penyelaras` (
  `id` int(6) NOT NULL,
  `idbilik_mesyuarat` int(6) DEFAULT NULL,
  `idpenyelaras` int(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## Konfigurasi

### Tetapan Pangkalan Data
Kemaskini tetapan sambungan dalam fail PHP:
```php
$host = 'localhost';
$dbname = 'db_tempahan_bm'; // Nama pangkalan data anda
$username = 'root';
$password = '';
```

### Tetapan Bahagian
Bahagian boleh dikustomisasi dalam fungsi `getNamaBahagian()`:
```php
function getNamaBahagian($bahagian_id) {
    $bahagian = [
        1 => 'Pentadbiran & Kesihatan Awam',
        2 => 'Perubatan',
        3 => 'Pengurusan',
        4 => 'Pergigian',
        5 => 'Farmasi',
        6 => 'Teknologi Maklumat dan Komunikasi',
        7 => 'Penyelidikan dan Pembangunan',
        8 => 'Latihan dan Pembangunan'
    ];
    return isset($bahagian[$bahagian_id]) ? $bahagian[$bahagian_id] : 'Tidak Diketahui';
}
```

## Fail-fail Sistem

1. **`bilik_mesyuarat_baru.sql`** - Data SQL untuk bilik baru
2. **`senarai_bilik_baru.php`** - Interface untuk melihat senarai bilik
3. **`tempah_bilik_baru.php`** - Borang tempahan bilik
4. **`README_BilikBaru.md`** - Dokumentasi sistem

## Ciri-ciri Tambahan yang Boleh Dibangunkan

- 📅 **Kalendar Tempahan**: Paparan kalendar untuk melihat tempahan
- 📧 **Notifikasi Email**: Hantar email pengesahan tempahan
- 📊 **Laporan Penggunaan**: Statistik penggunaan bilik
- 🔐 **Sistem Login**: Kawalan akses untuk pengguna
- 📱 **Mobile App**: Aplikasi mobile untuk tempahan
- 🔄 **API Integration**: API untuk integrasi dengan sistem lain

## Sokongan

Untuk sebarang pertanyaan atau masalah, sila hubungi:
- **Email**: <EMAIL>
- **Telefon**: 03-xxxx-xxxx
- **Jabatan ICT**: Tingkat 6, Blok Pentadbiran

---
*Sistem Tempahan Bilik Mesyuarat v2.0 - Dikemas kini dengan 48 bilik baru*
