<?php
/**
 * Tempahan Saya
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Tempahan Saya';
require_once 'config/sistem_config.php';

// Semak login
perluLogin();

// Parameter untuk penapis
$status_filter = $_GET['status'] ?? '';
$bulan_filter = $_GET['bulan'] ?? date('Y-m');

// Bina query untuk dapatkan tempahan pengguna
$where_conditions = ["t.pengguna_id = ?"];
$params = [$_SESSION['pengguna_id']];

if (!empty($status_filter)) {
    $where_conditions[] = "t.status = ?";
    $params[] = $status_filter;
}

if (!empty($bulan_filter)) {
    $where_conditions[] = "DATE_FORMAT(t.tarikh_tempahan, '%Y-%m') = ?";
    $params[] = $bulan_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    // Dapatkan senarai tempahan
    $sql = "SELECT t.*, b.nama_bilik, b.lokasi, b.kapasiti,
                   p.nama_penuh as nama_penyelulusan
            FROM tempahan t
            JOIN bilik_mesyuarat b ON t.bilik_id = b.id
            LEFT JOIN pengguna p ON t.diluluskan_oleh = p.id
            WHERE $where_clause
            ORDER BY t.tarikh_tempahan DESC, t.masa_mula DESC";
    
    $senarai_tempahan = $db->fetchAll($sql, $params);
    
    // Dapatkan statistik tempahan pengguna
    $sql_stats = "SELECT 
                    COUNT(*) as jumlah_keseluruhan,
                    SUM(CASE WHEN status = 'menunggu' THEN 1 ELSE 0 END) as menunggu,
                    SUM(CASE WHEN status = 'diluluskan' THEN 1 ELSE 0 END) as diluluskan,
                    SUM(CASE WHEN status = 'ditolak' THEN 1 ELSE 0 END) as ditolak,
                    SUM(CASE WHEN status = 'dibatalkan' THEN 1 ELSE 0 END) as dibatalkan,
                    SUM(CASE WHEN status = 'selesai' THEN 1 ELSE 0 END) as selesai
                  FROM tempahan 
                  WHERE pengguna_id = ?";
    $statistik = $db->fetch($sql_stats, [$_SESSION['pengguna_id']]);
    
} catch (Exception $e) {
    $senarai_tempahan = [];
    $statistik = [
        'jumlah_keseluruhan' => 0, 'menunggu' => 0, 'diluluskan' => 0,
        'ditolak' => 0, 'dibatalkan' => 0, 'selesai' => 0
    ];
    setMesejFlash('ralat', 'Ralat memuatkan data tempahan: ' . $e->getMessage());
}

// Proses pembatalan tempahan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['batal_tempahan'])) {
    $tempahan_id = (int)$_POST['tempahan_id'];
    
    try {
        // Semak jika tempahan boleh dibatalkan
        $sql_check = "SELECT * FROM tempahan WHERE id = ? AND pengguna_id = ? AND status IN ('menunggu', 'diluluskan')";
        $tempahan_check = $db->fetch($sql_check, [$tempahan_id, $_SESSION['pengguna_id']]);
        
        if ($tempahan_check) {
            // Update status tempahan
            $sql_update = "UPDATE tempahan SET status = 'dibatalkan' WHERE id = ?";
            $result = $db->query($sql_update, [$tempahan_id]);
            
            if ($result) {
                // Log aktiviti
                logAktiviti($_SESSION['pengguna_id'], 'Tempahan Dibatalkan', 
                           "Tempahan {$tempahan_check['kod_tempahan']} dibatalkan oleh pengguna");
                
                // Masukkan ke sejarah tempahan
                $sql_sejarah = "INSERT INTO sejarah_tempahan 
                                (tempahan_id, tindakan, dilakukan_oleh, catatan) 
                                VALUES (?, 'dibatalkan', ?, 'Dibatalkan oleh pengguna')";
                $db->query($sql_sejarah, [$tempahan_id, $_SESSION['pengguna_id']]);
                
                setMesejFlash('kejayaan', 'Tempahan berjaya dibatalkan.');
            } else {
                setMesejFlash('ralat', 'Ralat semasa membatalkan tempahan.');
            }
        } else {
            setMesejFlash('ralat', 'Tempahan tidak dijumpai atau tidak boleh dibatalkan.');
        }
        
        // Redirect untuk elakkan resubmission
        arahkanKe($_SERVER['REQUEST_URI']);
        
    } catch (Exception $e) {
        setMesejFlash('ralat', 'Ralat sistem: ' . $e->getMessage());
    }
}

require_once 'includes/header_sistem.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Tempahan Saya</h1>
                <p class="text-muted mb-0">Senarai tempahan bilik mesyuarat anda</p>
            </div>
            <div>
                <a href="tempah_bilik.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Tempahan Baru
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Tempahan -->
<div class="row mb-4">
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-primary mb-1"><?= $statistik['jumlah_keseluruhan'] ?></h5>
                <small class="text-muted">Jumlah</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-warning mb-1"><?= $statistik['menunggu'] ?></h5>
                <small class="text-muted">Menunggu</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-success mb-1"><?= $statistik['diluluskan'] ?></h5>
                <small class="text-muted">Diluluskan</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-danger mb-1"><?= $statistik['ditolak'] ?></h5>
                <small class="text-muted">Ditolak</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-secondary mb-1"><?= $statistik['dibatalkan'] ?></h5>
                <small class="text-muted">Dibatalkan</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-info mb-1"><?= $statistik['selesai'] ?></h5>
                <small class="text-muted">Selesai</small>
            </div>
        </div>
    </div>
</div>

<!-- Panel Penapis -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">Semua Status</option>
                        <option value="menunggu" <?= ($status_filter == 'menunggu') ? 'selected' : '' ?>>Menunggu</option>
                        <option value="diluluskan" <?= ($status_filter == 'diluluskan') ? 'selected' : '' ?>>Diluluskan</option>
                        <option value="ditolak" <?= ($status_filter == 'ditolak') ? 'selected' : '' ?>>Ditolak</option>
                        <option value="dibatalkan" <?= ($status_filter == 'dibatalkan') ? 'selected' : '' ?>>Dibatalkan</option>
                        <option value="selesai" <?= ($status_filter == 'selesai') ? 'selected' : '' ?>>Selesai</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="bulan" class="form-label">Bulan</label>
                    <input type="month" class="form-control" id="bulan" name="bulan" 
                           value="<?= htmlspecialchars($bulan_filter) ?>">
                </div>
                
                <div class="col-md-3">
                    <div class="btn-group w-100">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> Tapis
                        </button>
                        <a href="tempahan_saya.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Senarai Tempahan -->
<div class="row">
    <?php if (empty($senarai_tempahan)): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-calendar-x fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">Tiada tempahan dijumpai</h4>
                <p class="text-muted">Anda belum membuat sebarang tempahan atau tiada tempahan yang sepadan dengan kriteria penapis.</p>
                <a href="tempah_bilik.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Buat Tempahan Pertama
                </a>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($senarai_tempahan as $tempahan): ?>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <small class="text-muted"><?= htmlspecialchars($tempahan['kod_tempahan']) ?></small>
                        <?php
                        $badge_class = '';
                        $status_text = '';
                        switch ($tempahan['status']) {
                            case 'menunggu':
                                $badge_class = 'bg-warning';
                                $status_text = 'Menunggu';
                                break;
                            case 'diluluskan':
                                $badge_class = 'bg-success';
                                $status_text = 'Diluluskan';
                                break;
                            case 'ditolak':
                                $badge_class = 'bg-danger';
                                $status_text = 'Ditolak';
                                break;
                            case 'dibatalkan':
                                $badge_class = 'bg-secondary';
                                $status_text = 'Dibatalkan';
                                break;
                            case 'selesai':
                                $badge_class = 'bg-info';
                                $status_text = 'Selesai';
                                break;
                            default:
                                $badge_class = 'bg-light text-dark';
                                $status_text = ucfirst($tempahan['status']);
                        }
                        ?>
                        <span class="badge <?= $badge_class ?>"><?= $status_text ?></span>
                    </div>
                    
                    <div class="card-body">
                        <h6 class="card-title"><?= htmlspecialchars($tempahan['nama_bilik']) ?></h6>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i><?= formatTarikh($tempahan['tarikh_tempahan']) ?>
                            </small>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i><?= formatMasa($tempahan['masa_mula']) ?> - <?= formatMasa($tempahan['masa_tamat']) ?>
                            </small>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-geo-alt me-1"></i><?= htmlspecialchars($tempahan['lokasi']) ?>
                            </small>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-people me-1"></i><?= $tempahan['bilangan_peserta'] ?> peserta
                            </small>
                        </div>
                        
                        <p class="card-text small">
                            <strong>Tujuan:</strong> <?= htmlspecialchars($tempahan['tujuan']) ?>
                        </p>
                        
                        <?php if ($tempahan['jumlah_kos'] > 0): ?>
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="bi bi-currency-dollar me-1"></i>Kos: <?= formatWang($tempahan['jumlah_kos']) ?>
                                </small>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($tempahan['catatan_pentadbir'])): ?>
                            <div class="alert alert-info alert-sm py-2">
                                <small><strong>Catatan:</strong> <?= htmlspecialchars($tempahan['catatan_pentadbir']) ?></small>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                Dibuat: <?= formatTarikhMasa($tempahan['tarikh_tempahan_dibuat']) ?>
                            </small>
                            
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#modalTempahan<?= $tempahan['id'] ?>">
                                    <i class="bi bi-eye"></i>
                                </button>
                                
                                <?php if (in_array($tempahan['status'], ['menunggu', 'diluluskan'])): ?>
                                    <button class="btn btn-outline-danger" 
                                            onclick="batalkanTempahan(<?= $tempahan['id'] ?>, '<?= htmlspecialchars($tempahan['kod_tempahan']) ?>')">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal Detail Tempahan -->
            <div class="modal fade" id="modalTempahan<?= $tempahan['id'] ?>" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Detail Tempahan - <?= htmlspecialchars($tempahan['kod_tempahan']) ?></h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Maklumat Tempahan</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Kod Tempahan:</strong></td>
                                            <td><?= htmlspecialchars($tempahan['kod_tempahan']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td><span class="badge <?= $badge_class ?>"><?= $status_text ?></span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Tarikh:</strong></td>
                                            <td><?= formatTarikh($tempahan['tarikh_tempahan']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Masa:</strong></td>
                                            <td><?= formatMasa($tempahan['masa_mula']) ?> - <?= formatMasa($tempahan['masa_tamat']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Bilangan Peserta:</strong></td>
                                            <td><?= $tempahan['bilangan_peserta'] ?> orang</td>
                                        </tr>
                                        <?php if ($tempahan['jumlah_kos'] > 0): ?>
                                        <tr>
                                            <td><strong>Jumlah Kos:</strong></td>
                                            <td><?= formatWang($tempahan['jumlah_kos']) ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Maklumat Bilik</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Nama Bilik:</strong></td>
                                            <td><?= htmlspecialchars($tempahan['nama_bilik']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Lokasi:</strong></td>
                                            <td><?= htmlspecialchars($tempahan['lokasi']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Kapasiti:</strong></td>
                                            <td><?= $tempahan['kapasiti'] ?> orang</td>
                                        </tr>
                                        <?php if (!empty($tempahan['nama_penyelulusan'])): ?>
                                        <tr>
                                            <td><strong>Diluluskan Oleh:</strong></td>
                                            <td><?= htmlspecialchars($tempahan['nama_penyelulusan']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Tarikh Kelulusan:</strong></td>
                                            <td><?= formatTarikhMasa($tempahan['tarikh_kelulusan']) ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <h6>Tujuan Mesyuarat</h6>
                                    <p><?= nl2br(htmlspecialchars($tempahan['tujuan'])) ?></p>
                                    
                                    <?php if (!empty($tempahan['agenda'])): ?>
                                        <h6>Agenda</h6>
                                        <p><?= nl2br(htmlspecialchars($tempahan['agenda'])) ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($tempahan['keperluan_khas'])): ?>
                                        <h6>Keperluan Khas</h6>
                                        <p><?= nl2br(htmlspecialchars($tempahan['keperluan_khas'])) ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($tempahan['catatan_pentadbir'])): ?>
                                        <h6>Catatan Pentadbir</h6>
                                        <div class="alert alert-info">
                                            <?= nl2br(htmlspecialchars($tempahan['catatan_pentadbir'])) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                            <?php if (in_array($tempahan['status'], ['menunggu', 'diluluskan'])): ?>
                                <button type="button" class="btn btn-danger" 
                                        onclick="batalkanTempahan(<?= $tempahan['id'] ?>, '<?= htmlspecialchars($tempahan['kod_tempahan']) ?>')">
                                    <i class="bi bi-x-circle me-2"></i>Batalkan Tempahan
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Form tersembunyi untuk pembatalan -->
<form id="formBatal" method="POST" style="display: none;">
    <input type="hidden" name="batal_tempahan" value="1">
    <input type="hidden" name="tempahan_id" id="batalTempahan">
</form>

<?php 
$custom_js = "
function batalkanTempahan(tempahanId, kodTempahan) {
    if (confirm('Adakah anda pasti ingin membatalkan tempahan ' + kodTempahan + '?\\n\\nTindakan ini tidak boleh dibatalkan.')) {
        document.getElementById('batalTempahan').value = tempahanId;
        document.getElementById('formBatal').submit();
    }
}
";

require_once 'includes/footer_sistem.php'; 
?>
