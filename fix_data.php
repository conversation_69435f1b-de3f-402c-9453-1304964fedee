<?php
/**
 * Fix Data - Pastikan Data Dimasukkan Dengan Betul
 */

// Konfigurasi pangkalan data
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix Data - Sistem Tempahan Bilik Mesyuarat</h2>";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 1. Kosongkan dan masukkan data bahagian
    echo "<h3>1. Memasukkan Data Bahagian</h3>";
    $pdo->exec("DELETE FROM tbahagian");
    
    $bahagian_data = [
        [1, '<PERSON><PERSON><PERSON><PERSON>wa<PERSON>'],
        [2, 'Perubatan'],
        [3, 'Pen<PERSON>rusan'],
        [4, 'Pergigian'],
        [5, '<PERSON>asi'],
        [6, 'Keselamatan & Ku<PERSON>ti Ma<PERSON>']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO tbahagian (id, bahagian, idptj) VALUES (?, ?, NULL)");
    foreach ($bahagian_data as $data) {
        $stmt->execute($data);
        echo "<p>✓ Bahagian: {$data[1]}</p>";
    }
    
    // 2. Kosongkan dan masukkan data gred (sebahagian sahaja untuk test)
    echo "<h3>2. Memasukkan Data Gred</h3>";
    $pdo->exec("DELETE FROM tgred");
    
    $gred_data = [
        [1, 'B22'], [2, 'C27'], [3, 'C32'], [4, 'C41'], [5, 'C44'],
        [6, 'C48'], [7, 'C52'], [8, 'F29'], [9, 'F32'], [10, 'F41'],
        [11, 'F44'], [12, 'FT17'], [13, 'J17'], [14, 'J29'], [15, 'J41'],
        [16, 'J44'], [17, 'J48'], [18, 'KP17'], [19, 'M41'], [20, 'M44'],
        [21, 'M48'], [22, 'M52'], [23, 'N1'], [24, 'N17'], [25, 'N22'],
        [26, 'N26'], [27, 'N27'], [28, 'N28'], [29, 'N32'], [30, 'N36'],
        [31, 'N4'], [32, 'N41'], [33, 'R1'], [34, 'R3'], [35, 'R4'],
        [36, 'R6'], [37, 'S41'], [38, 'S44'], [39, 'S48'], [40, 'U17'],
        [41, 'U29'], [42, 'U32'], [43, 'U36'], [44, 'U38'], [45, 'U41'],
        [46, 'U42'], [47, 'U44'], [48, 'U48'], [49, 'U52'], [50, 'U54'],
        [51, 'UD44'], [52, 'UD48'], [53, 'UD51'], [54, 'UD52'], [55, 'UD54'],
        [56, 'W17'], [57, 'W22'], [58, 'W27'], [59, 'W36'], [60, 'W44'],
        [62, 'M48'], [63, 'M54'], [64, 'H11'], [65, 'N11'], [66, 'R11']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO tgred (id, gred) VALUES (?, ?)");
    foreach ($gred_data as $data) {
        $stmt->execute($data);
    }
    echo "<p>✓ " . count($gred_data) . " gred dimasukkan</p>";
    
    // 3. Kosongkan dan masukkan data jawatan
    echo "<h3>3. Memasukkan Data Jawatan</h3>";
    $pdo->exec("DELETE FROM tjawatan");
    
    $jawatan_data = [
        [2, 'JURUAUDIO VISUAL'],
        [3, 'JURURAWAT'],
        [4, 'JURURAWAT PERGIGIAN'],
        [5, 'JURUTEKNIK'],
        [6, 'JURUTEKNIK KOMPUTER'],
        [7, 'JURUTEKNOLOGI MAKMAL PERUBATAN'],
        [8, 'JURUTERA (AWAM)'],
        [9, 'JURUTERA (ELEKTRIK)'],
        [10, 'JURUTERA (KESIHATAN UMUM)'],
        [11, 'JURUTERA (MEKANIKAL)'],
        [12, 'PEGAWAI FARMASI'],
        [14, 'PEGAWAI KAUNSELOR'],
        [15, 'PEGAWAI KESIHATAN PERSEKITARAN'],
        [16, 'PEGAWAI KHIDMAT PELANGGAN'],
        [18, 'PEGAWAI PERGIGIAN'],
        [19, 'PEGAWAI PERGIGIAN'],
        [20, 'PEGAWAI PERUBATAN'],
        [22, 'PEGAWAI SAINS'],
        [23, 'PEGAWAI SAINS (KIMIA HAYAT)'],
        [24, 'PEGAWAI SAINS (PEGAWAI ZAT MAKANAN)'],
        [26, 'PEGAWAI TADBIR DAN DIPLOMATIK'],
        [27, 'PEGAWAI TEKNOLOGI MAKANAN'],
        [28, 'PEGAWAI TEKNOLOGI MAKLUMAT'],
        [29, 'PEKERJA AWAM'],
        [30, 'PEKERJA RENDAH AWAM'],
        [31, 'PEMANDU KENDERAAN'],
        [32, 'PEMBANTU AM PEJABAT'],
        [33, 'PEMBANTU KESELAMATAN'],
        [34, 'PEMBANTU KESIHATAN AWAM'],
        [35, 'PEMBANTU TADBIR (KESETIAUSAHAAN)'],
        [36, 'PEMBANTU TADBIR (KEWANGAN)'],
        [37, 'PEMBANTU TADBIR (P/O)'],
        [39, 'PEMBANTU TEKNIK'],
        [40, 'PEN. PEG. TEKNOLOGI MAKANAN'],
        [41, 'PENOLONG AKAUNTAN'],
        [42, 'PENOLONG JURUTERA'],
        [43, 'PENOLONG PEGAWAI KESIHATAN PERSEKITARAN'],
        [44, 'PENOLONG PEGAWAI PERUBATAN'],
        [45, 'PENOLONG PEGAWAI SAINS'],
        [46, 'PENOLONG PEGAWAI TADBIR'],
        [47, 'PEN. PEGAWAI TADBIR (REKOD PERUBATAN)'],
        [48, 'PEN. PEGAWAI TEKNOLOGI MAKLUMAT'],
        [49, 'PEREKA'],
        [50, 'SETIAUSAHA PEJABAT'],
        [52, 'TIMB. PENGARAH KESIHATAN NEGERI (PENGURUSAN)'],
        [53, 'PENGARAH KESIHATAN NEGERI'],
        [54, 'PENGARAH HOSPITAL']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO tjawatan (id, jawatan) VALUES (?, ?)");
    foreach ($jawatan_data as $data) {
        $stmt->execute($data);
    }
    echo "<p>✓ " . count($jawatan_data) . " jawatan dimasukkan</p>";
    
    // Enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Semak data yang dimasukkan
    echo "<h3>4. Semakan Data</h3>";
    
    $bahagian_count = $pdo->query("SELECT COUNT(*) FROM tbahagian")->fetchColumn();
    $jawatan_count = $pdo->query("SELECT COUNT(*) FROM tjawatan")->fetchColumn();
    $gred_count = $pdo->query("SELECT COUNT(*) FROM tgred")->fetchColumn();
    
    echo "<p><strong>Jumlah Bahagian:</strong> $bahagian_count</p>";
    echo "<p><strong>Jumlah Jawatan:</strong> $jawatan_count</p>";
    echo "<p><strong>Jumlah Gred:</strong> $gred_count</p>";
    
    // Test dropdown
    echo "<h3>5. Test Dropdown</h3>";
    $bahagian_list = $pdo->query("SELECT id, bahagian FROM tbahagian ORDER BY bahagian")->fetchAll();
    
    echo "<select style='width: 300px; padding: 5px;'>";
    echo "<option value=''>Pilih Bahagian</option>";
    foreach ($bahagian_list as $b) {
        echo "<option value='{$b['id']}'>" . htmlspecialchars($b['bahagian']) . "</option>";
    }
    echo "</select>";
    
    echo "<h3 style='color: green;'>✓ Data berjaya dimasukkan!</h3>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='daftar.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Daftar</a>";
    echo "<a href='debug_dropdown.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Debug Dropdown</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h3 { color: #555; margin-top: 25px; }
        p { margin: 8px 0; }
        a { display: inline-block; margin: 5px; }
    </style>
</head>
<body>
</body>
</html>
