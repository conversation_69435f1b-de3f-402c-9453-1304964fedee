<?php
/**
 * <PERSON><PERSON>dar Tempahan
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kalendar Tempahan';
require_once 'config/sistem_config.php';

// Semak login
perluLogin();

// Parameter bulan dan tahun
$bulan = $_GET['bulan'] ?? date('m');
$tahun = $_GET['tahun'] ?? date('Y');
$bilik_filter = $_GET['bilik'] ?? '';

// Validasi input
$bulan = (int)$bulan;
$tahun = (int)$tahun;
if ($bulan < 1 || $bulan > 12) $bulan = date('m');
if ($tahun < 2020 || $tahun > 2030) $tahun = date('Y');

// Dapatkan tempahan untuk bulan ini
try {
    $where_conditions = ["DATE_FORMAT(t.tarikh_tempahan, '%Y-%m') = ?"];
    $params = [sprintf('%04d-%02d', $tahun, $bulan)];
    
    if (!empty($bilik_filter)) {
        $where_conditions[] = "t.bilik_id = ?";
        $params[] = $bilik_filter;
    }
    
    // Jika bukan pentadbir, hanya tunjukkan tempahan sendiri dan yang diluluskan
    if (!semakPeranan('pentadbir')) {
        $where_conditions[] = "(t.pengguna_id = ? OR t.status = 'diluluskan')";
        $params[] = $_SESSION['pengguna_id'];
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT t.*, b.nama_bilik, b.lokasi, p.nama_penuh as nama_pengguna
            FROM tempahan t
            JOIN bilik_mesyuarat b ON t.bilik_id = b.id
            JOIN pengguna p ON t.pengguna_id = p.id
            WHERE $where_clause
            AND t.status IN ('menunggu', 'diluluskan', 'selesai')
            ORDER BY t.tarikh_tempahan, t.masa_mula";
    
    $tempahan_bulan = $db->fetchAll($sql, $params);
    
    // Susun tempahan mengikut tarikh
    $tempahan_by_date = [];
    foreach ($tempahan_bulan as $tempahan) {
        $tarikh = $tempahan['tarikh_tempahan'];
        if (!isset($tempahan_by_date[$tarikh])) {
            $tempahan_by_date[$tarikh] = [];
        }
        $tempahan_by_date[$tarikh][] = $tempahan;
    }
    
    // Dapatkan senarai bilik untuk filter
    $sql_bilik = "SELECT id, nama_bilik FROM bilik_mesyuarat WHERE status = 'tersedia' ORDER BY nama_bilik";
    $senarai_bilik = $db->fetchAll($sql_bilik);
    
} catch (Exception $e) {
    $tempahan_by_date = [];
    $senarai_bilik = [];
    setMesejFlash('ralat', 'Ralat memuatkan data kalendar: ' . $e->getMessage());
}

// Fungsi untuk jana kalendar
function janaKalendar($bulan, $tahun, $tempahan_by_date) {
    $hari_pertama = mktime(0, 0, 0, $bulan, 1, $tahun);
    $nama_bulan = date('F Y', $hari_pertama);
    $hari_dalam_bulan = date('t', $hari_pertama);
    $hari_minggu_pertama = date('w', $hari_pertama);
    
    $kalendar = "<div class='calendar-grid'>";
    
    // Header hari
    $nama_hari = ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'];
    foreach ($nama_hari as $hari) {
        $kalendar .= "<div class='calendar-header'>$hari</div>";
    }
    
    // Hari kosong sebelum hari pertama
    for ($i = 0; $i < $hari_minggu_pertama; $i++) {
        $kalendar .= "<div class='calendar-day empty'></div>";
    }
    
    // Hari dalam bulan
    for ($hari = 1; $hari <= $hari_dalam_bulan; $hari++) {
        $tarikh = sprintf('%04d-%02d-%02d', $tahun, $bulan, $hari);
        $hari_ini = ($tarikh == date('Y-m-d')) ? 'today' : '';
        $ada_tempahan = isset($tempahan_by_date[$tarikh]) ? 'has-booking' : '';
        
        $kalendar .= "<div class='calendar-day $hari_ini $ada_tempahan' data-date='$tarikh'>";
        $kalendar .= "<div class='day-number'>$hari</div>";
        
        if (isset($tempahan_by_date[$tarikh])) {
            $jumlah = count($tempahan_by_date[$tarikh]);
            $kalendar .= "<div class='booking-count'>$jumlah tempahan</div>";
        }
        
        $kalendar .= "</div>";
    }
    
    $kalendar .= "</div>";
    return $kalendar;
}

require_once 'includes/header_sistem.php';
?>

<style>
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #dee2e6;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-header {
    background-color: var(--primary-color);
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-day {
    background-color: white;
    min-height: 80px;
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day.empty {
    background-color: #f8f9fa;
    cursor: default;
}

.calendar-day.today {
    background-color: #e3f2fd;
    border: 2px solid var(--primary-color);
}

.calendar-day.has-booking {
    background-color: #fff3cd;
}

.calendar-day.has-booking:hover {
    background-color: #ffeaa7;
}

.day-number {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.booking-count {
    font-size: 0.75rem;
    color: #6c757d;
    background-color: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 10px;
    text-align: center;
}

.calendar-day.today .day-number {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .calendar-day {
        min-height: 60px;
        padding: 4px;
    }
    
    .day-number {
        font-size: 1rem;
    }
    
    .booking-count {
        font-size: 0.7rem;
    }
}
</style>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Kalendar Tempahan</h1>
                <p class="text-muted mb-0">Lihat tempahan bilik mesyuarat dalam bentuk kalendar</p>
            </div>
            <div>
                <a href="tempah_bilik.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Tempahan Baru
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Kawalan Kalendar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="">
            <div class="row g-3 align-items-end">
                <div class="col-md-2">
                    <label for="bulan" class="form-label">Bulan</label>
                    <select class="form-select" id="bulan" name="bulan">
                        <?php
                        $nama_bulan = [
                            1 => 'Januari', 2 => 'Februari', 3 => 'Mac', 4 => 'April',
                            5 => 'Mei', 6 => 'Jun', 7 => 'Julai', 8 => 'Ogos',
                            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Disember'
                        ];
                        for ($i = 1; $i <= 12; $i++):
                        ?>
                            <option value="<?= $i ?>" <?= ($i == $bulan) ? 'selected' : '' ?>>
                                <?= $nama_bulan[$i] ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="tahun" class="form-label">Tahun</label>
                    <select class="form-select" id="tahun" name="tahun">
                        <?php for ($y = 2020; $y <= 2030; $y++): ?>
                            <option value="<?= $y ?>" <?= ($y == $tahun) ? 'selected' : '' ?>><?= $y ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="bilik" class="form-label">Bilik</label>
                    <select class="form-select" id="bilik" name="bilik">
                        <option value="">Semua Bilik</option>
                        <?php foreach ($senarai_bilik as $bilik): ?>
                            <option value="<?= $bilik['id'] ?>" <?= ($bilik['id'] == $bilik_filter) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($bilik['nama_bilik']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> Papar
                    </button>
                </div>
                
                <div class="col-md-3">
                    <div class="btn-group w-100">
                        <a href="?bulan=<?= date('m') ?>&tahun=<?= date('Y') ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-calendar-today"></i> Bulan Ini
                        </a>
                        <a href="tempahan_saya.php" class="btn btn-outline-primary">
                            <i class="bi bi-list"></i> Senarai
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Kalendar -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-calendar3 me-2"></i>
            <?php
            $nama_bulan_display = [
                1 => 'Januari', 2 => 'Februari', 3 => 'Mac', 4 => 'April',
                5 => 'Mei', 6 => 'Jun', 7 => 'Julai', 8 => 'Ogos',
                9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Disember'
            ];
            echo $nama_bulan_display[$bulan] . ' ' . $tahun;
            ?>
        </h5>
        
        <div class="btn-group btn-group-sm">
            <?php
            $bulan_sebelum = $bulan - 1;
            $tahun_sebelum = $tahun;
            if ($bulan_sebelum < 1) {
                $bulan_sebelum = 12;
                $tahun_sebelum--;
            }
            
            $bulan_seterusnya = $bulan + 1;
            $tahun_seterusnya = $tahun;
            if ($bulan_seterusnya > 12) {
                $bulan_seterusnya = 1;
                $tahun_seterusnya++;
            }
            ?>
            <a href="?bulan=<?= $bulan_sebelum ?>&tahun=<?= $tahun_sebelum ?>&bilik=<?= $bilik_filter ?>" 
               class="btn btn-outline-primary">
                <i class="bi bi-chevron-left"></i>
            </a>
            <a href="?bulan=<?= $bulan_seterusnya ?>&tahun=<?= $tahun_seterusnya ?>&bilik=<?= $bilik_filter ?>" 
               class="btn btn-outline-primary">
                <i class="bi bi-chevron-right"></i>
            </a>
        </div>
    </div>
    
    <div class="card-body p-0">
        <?= janaKalendar($bulan, $tahun, $tempahan_by_date) ?>
    </div>
</div>

<!-- Legenda -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6><i class="bi bi-info-circle me-2"></i>Legenda</h6>
                <div class="d-flex flex-wrap gap-3">
                    <div class="d-flex align-items-center">
                        <div class="calendar-day today me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px;"></div>
                        <small>Hari Ini</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="calendar-day has-booking me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px;"></div>
                        <small>Ada Tempahan</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="calendar-day me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px; background-color: white; border: 1px solid #dee2e6;"></div>
                        <small>Tiada Tempahan</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6><i class="bi bi-bar-chart me-2"></i>Statistik Bulan Ini</h6>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="h5 text-primary mb-0"><?= count($tempahan_bulan) ?></div>
                        <small class="text-muted">Jumlah Tempahan</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-success mb-0">
                            <?= count(array_filter($tempahan_bulan, function($t) { return $t['status'] == 'diluluskan'; })) ?>
                        </div>
                        <small class="text-muted">Diluluskan</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-warning mb-0">
                            <?= count(array_filter($tempahan_bulan, function($t) { return $t['status'] == 'menunggu'; })) ?>
                        </div>
                        <small class="text-muted">Menunggu</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Hari -->
<div class="modal fade" id="modalDetailHari" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tempahan pada <span id="modalTarikh"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="#" id="btnTempahHari" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Tempah untuk Hari Ini
                </a>
            </div>
        </div>
    </div>
</div>

<?php 
$custom_js = "
// Data tempahan untuk JavaScript
const tempahanData = " . json_encode($tempahan_by_date) . ";

// Event listener untuk klik hari
document.querySelectorAll('.calendar-day:not(.empty)').forEach(function(day) {
    day.addEventListener('click', function() {
        const date = this.dataset.date;
        const tempahan = tempahanData[date] || [];
        
        document.getElementById('modalTarikh').textContent = formatTarikh(date);
        document.getElementById('btnTempahHari').href = 'tempah_bilik.php?tarikh=' + date;
        
        let content = '';
        if (tempahan.length === 0) {
            content = '<div class=\"text-center py-4\"><i class=\"bi bi-calendar-x fs-1 text-muted mb-3\"></i><p class=\"text-muted\">Tiada tempahan pada hari ini.</p></div>';
        } else {
            content = '<div class=\"list-group\">';
            tempahan.forEach(function(t) {
                const statusClass = getStatusClass(t.status);
                content += '<div class=\"list-group-item\">';
                content += '<div class=\"d-flex justify-content-between align-items-start\">';
                content += '<div class=\"flex-grow-1\">';
                content += '<h6 class=\"mb-1\">' + escapeHtml(t.nama_bilik) + '</h6>';
                content += '<p class=\"mb-1\"><strong>Masa:</strong> ' + t.masa_mula.substring(0,5) + ' - ' + t.masa_tamat.substring(0,5) + '</p>';
                content += '<p class=\"mb-1\"><strong>Tujuan:</strong> ' + escapeHtml(t.tujuan) + '</p>';
                content += '<small class=\"text-muted\">Penempah: ' + escapeHtml(t.nama_pengguna) + '</small>';
                content += '</div>';
                content += '<span class=\"badge ' + statusClass + '\">' + getStatusText(t.status) + '</span>';
                content += '</div></div>';
            });
            content += '</div>';
        }
        
        document.getElementById('modalContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('modalDetailHari')).show();
    });
});

function formatTarikh(dateString) {
    const date = new Date(dateString + 'T00:00:00');
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    return date.toLocaleDateString('ms-MY', options);
}

function getStatusClass(status) {
    switch(status) {
        case 'menunggu': return 'bg-warning';
        case 'diluluskan': return 'bg-success';
        case 'ditolak': return 'bg-danger';
        case 'dibatalkan': return 'bg-secondary';
        case 'selesai': return 'bg-info';
        default: return 'bg-light text-dark';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'menunggu': return 'Menunggu';
        case 'diluluskan': return 'Diluluskan';
        case 'ditolak': return 'Ditolak';
        case 'dibatalkan': return 'Dibatalkan';
        case 'selesai': return 'Selesai';
        default: return status;
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
";

require_once 'includes/footer_sistem.php'; 
?>
