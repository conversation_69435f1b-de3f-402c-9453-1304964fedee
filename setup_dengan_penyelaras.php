<?php
/**
 * Setup Database Dengan Penyelaras Bilik
 * Sistem Tempahan Bilik Mesyuarat
 */

// Konfigurasi pangkalan data
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    // Sambung ke MySQL
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setup Database Dengan Penyelaras Bilik</h2>";
    
    // Cipta database
    echo "<p>1. Mencipta pangkalan data '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE $database");
    echo "<p style='color: green;'>✓ Database siap</p>";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    echo "<p>2. Mencipta jadual...</p>";
    
    // Drop jadual lama
    $drop_tables = [
        "DROP TABLE IF EXISTS `tbilik_penyelaras`",
        "DROP TABLE IF EXISTS `tempahan`",
        "DROP TABLE IF EXISTS `tbilik_mesyuarat`",
        "DROP TABLE IF EXISTS `bilik_mesyuarat`",
        "DROP TABLE IF EXISTS `pengguna`",
        "DROP TABLE IF EXISTS `tunit`",
        "DROP TABLE IF EXISTS `tjawatan`",
        "DROP TABLE IF EXISTS `tgred`",
        "DROP TABLE IF EXISTS `tbahagian`"
    ];
    
    foreach ($drop_tables as $sql) {
        $pdo->exec($sql);
    }
    echo "<p style='color: green;'>✓ Jadual lama dibuang</p>";
    
    // Cipta jadual baru
    $create_tables = [
        // Jadual Bahagian
        "CREATE TABLE `tbahagian` (
            `id` int(6) NOT NULL,
            `bahagian` varchar(100) DEFAULT NULL,
            `idptj` int(6) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Unit
        "CREATE TABLE `tunit` (
            `id` int(6) NOT NULL,
            `unit` varchar(100) DEFAULT NULL,
            `idbahagian` int(6) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `fk_unit_bahagian` (`idbahagian`),
            CONSTRAINT `fk_unit_bahagian` FOREIGN KEY (`idbahagian`) REFERENCES `tbahagian` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Gred
        "CREATE TABLE `tgred` (
            `id` int(11) NOT NULL,
            `gred` varchar(10) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Jawatan
        "CREATE TABLE `tjawatan` (
            `id` int(100) NOT NULL,
            `jawatan` varchar(100) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Pengguna
        "CREATE TABLE pengguna (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nokp VARCHAR(12) UNIQUE NOT NULL,
            kata_laluan VARCHAR(255) NOT NULL,
            nama_penuh VARCHAR(100) NOT NULL,
            emel VARCHAR(100) UNIQUE NOT NULL,
            no_telefon VARCHAR(20),
            bahagian_id INT,
            unit_id INT,
            jawatan_id INT,
            gred_id INT,
            peranan ENUM('pentadbir', 'pengguna', 'penyelaras') DEFAULT 'pengguna',
            status ENUM('aktif', 'tidak_aktif') DEFAULT 'aktif',
            tarikh_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (bahagian_id) REFERENCES tbahagian(id) ON DELETE SET NULL,
            FOREIGN KEY (unit_id) REFERENCES tunit(id) ON DELETE SET NULL,
            FOREIGN KEY (jawatan_id) REFERENCES tjawatan(id) ON DELETE SET NULL,
            FOREIGN KEY (gred_id) REFERENCES tgred(id) ON DELETE SET NULL
        )",
        
        // Jadual Bilik Mesyuarat
        "CREATE TABLE tbilik_mesyuarat (
            id INT PRIMARY KEY,
            nama_bilik_mesyuarat VARCHAR(100) NOT NULL,
            kapasiti INT NOT NULL,
            bahagian INT,
            lokasi VARCHAR(200),
            tingkat VARCHAR(20),
            penerangan TEXT,
            kemudahan JSON,
            kadar_sejam DECIMAL(10,2) DEFAULT 0.00,
            gambar_url VARCHAR(255),
            status ENUM('tersedia', 'tidak_tersedia', 'penyelenggaraan') DEFAULT 'tersedia',
            tarikh_cipta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (bahagian) REFERENCES tbahagian(id) ON DELETE SET NULL
        )",
        
        // Jadual Penyelaras Bilik
        "CREATE TABLE tbilik_penyelaras (
            id INT PRIMARY KEY,
            idbilik_mesyuarat INT,
            idpenyelaras INT,
            FOREIGN KEY (idbilik_mesyuarat) REFERENCES tbilik_mesyuarat(id) ON DELETE CASCADE,
            FOREIGN KEY (idpenyelaras) REFERENCES pengguna(id) ON DELETE CASCADE
        )",
        
        // Jadual Tempahan
        "CREATE TABLE tempahan (
            id INT AUTO_INCREMENT PRIMARY KEY,
            kod_tempahan VARCHAR(20) UNIQUE NOT NULL,
            pengguna_id INT NOT NULL,
            bilik_id INT NOT NULL,
            tarikh_tempahan DATE NOT NULL,
            masa_mula TIME NOT NULL,
            masa_tamat TIME NOT NULL,
            tujuan VARCHAR(255) NOT NULL,
            agenda TEXT,
            bilangan_peserta INT DEFAULT 1,
            keperluan_khas TEXT,
            status ENUM('menunggu', 'diluluskan', 'ditolak', 'dibatalkan', 'selesai') DEFAULT 'menunggu',
            jumlah_kos DECIMAL(10,2) DEFAULT 0.00,
            catatan_pentadbir TEXT,
            diluluskan_oleh INT,
            tarikh_kelulusan TIMESTAMP NULL,
            tarikh_tempahan_dibuat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (pengguna_id) REFERENCES pengguna(id) ON DELETE CASCADE,
            FOREIGN KEY (bilik_id) REFERENCES tbilik_mesyuarat(id) ON DELETE CASCADE,
            FOREIGN KEY (diluluskan_oleh) REFERENCES pengguna(id) ON DELETE SET NULL,
            UNIQUE KEY tempahan_unik (bilik_id, tarikh_tempahan, masa_mula, masa_tamat)
        )"
    ];
    
    foreach ($create_tables as $sql) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✓ Jadual dicipta</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Ralat: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>3. Memasukkan data...</p>";
    
    // Insert data bahagian
    $pdo->exec("INSERT INTO `tbahagian`(`id`,`bahagian`,`idptj`) VALUES 
        (1,'Kesihatan Awam',NULL),
        (2,'Perubatan',NULL),
        (3,'Pengurusan',NULL),
        (4,'Pergigian',NULL),
        (5,'Farmasi',NULL),
        (6,'Keselamatan & Kualiti Makanan',NULL)");
    echo "<p style='color: green;'>✓ Data bahagian</p>";
    
    // Insert data unit (sebahagian)
    $pdo->exec("INSERT INTO `tunit`(`id`,`unit`,`idbahagian`) VALUES 
        (2,'ALAM SEKITAR',1),
        (5,'KESIHATAN AWAM',1),
        (15,'Pengurusan Perubatan',2),
        (22,'PENGARAH',3),
        (58,'Pengurusan Pergigian',4),
        (32,'Pengurusan Farmasi',5),
        (34,'BKKM',6)");
    echo "<p style='color: green;'>✓ Data unit</p>";
    
    // Insert data gred (sebahagian)
    $pdo->exec("INSERT INTO `tgred`(`id`,`gred`) VALUES 
        (32,'N41'), (45,'U41'), (47,'U44'), (3,'C32'), (20,'M44')");
    echo "<p style='color: green;'>✓ Data gred</p>";
    
    // Insert data jawatan (sebahagian)
    $pdo->exec("INSERT INTO `tjawatan`(`id`,`jawatan`) VALUES 
        (26,'PEGAWAI TADBIR DAN DIPLOMATIK'),
        (20,'PEGAWAI PERUBATAN'),
        (3,'JURURAWAT'),
        (12,'PEGAWAI FARMASI'),
        (18,'PEGAWAI PERGIGIAN')");
    echo "<p style='color: green;'>✓ Data jawatan</p>";
    
    // Insert pengguna
    $pdo->exec("INSERT INTO pengguna (nokp, kata_laluan, nama_penuh, emel, peranan, bahagian_id, unit_id, jawatan_id, gred_id) VALUES
        ('123456789012', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Pentadbir Sistem', '<EMAIL>', 'pentadbir', 3, 22, 26, 32),
        ('234567890123', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. Ahmad bin Ali', '<EMAIL>', 'penyelaras', 2, 15, 20, 45),
        ('345678901234', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Siti binti Hassan', '<EMAIL>', 'pengguna', 1, 5, 3, 47)");
    echo "<p style='color: green;'>✓ Data pengguna</p>";
    
    // Insert bilik mesyuarat
    $pdo->exec("INSERT INTO `tbilik_mesyuarat`(`id`,`nama_bilik_mesyuarat`,`kapasiti`,`bahagian`) VALUES 
        (1,'Bilik Mesyuarat Utama',60,3),
        (2,'Bilik Command Centre',25,1),
        (3,'Bilik Mesyuarat Kesihatan Awam',35,1),
        (4,'Bilik Kaunseling Kelompok',8,3),
        (5,'Bilik Mesyuarat Perubatan',20,2),
        (6,'Bilik Mesyuarat Pengurusan',35,3),
        (7,'Bilik Perbincangan Pengarah',10,3),
        (8,'Bilik Mesyuarat Pergigian',25,4),
        (9,'Bilik Sumber Kualiti Perubatan',10,2),
        (10,'Bilik Mesyuarat Farmasi',35,5),
        (12,'TESTING BILIK (HANYA UNTUK PENGUJIAN ICT SAHAJA)',10,3)");
    echo "<p style='color: green;'>✓ Data bilik mesyuarat</p>";
    
    // Insert penyelaras bilik
    $pdo->exec("INSERT INTO `tbilik_penyelaras`(`id`,`idbilik_mesyuarat`,`idpenyelaras`) VALUES 
        (1,1,1), (6,4,1), (8,6,1), (10,7,1), (12,5,2), (17,8,2), (23,6,1), (24,1,1), 
        (25,7,1), (27,10,2), (30,9,2), (34,2,1), (36,3,3), (38,5,2), (39,5,2), 
        (40,5,2), (42,12,1), (43,12,1), (44,12,1), (47,12,1), (50,10,2)");
    echo "<p style='color: green;'>✓ Data penyelaras bilik</p>";
    
    // Enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Semak data
    echo "<p>4. Menyemak data...</p>";
    $bilik_count = $pdo->query("SELECT COUNT(*) FROM tbilik_mesyuarat")->fetchColumn();
    $penyelaras_count = $pdo->query("SELECT COUNT(*) FROM tbilik_penyelaras")->fetchColumn();
    $pengguna_count = $pdo->query("SELECT COUNT(*) FROM pengguna")->fetchColumn();
    
    echo "<p>Bilik Mesyuarat: $bilik_count</p>";
    echo "<p>Penyelaras Bilik: $penyelaras_count</p>";
    echo "<p>Pengguna: $pengguna_count</p>";
    
    // Test query penyelaras
    echo "<h4>Senarai Bilik dan Penyelaras:</h4>";
    $stmt = $pdo->query("SELECT b.nama_bilik_mesyuarat, p.nama_penuh as penyelaras
                         FROM tbilik_mesyuarat b
                         LEFT JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                         LEFT JOIN pengguna p ON bp.idpenyelaras = p.id
                         ORDER BY b.nama_bilik_mesyuarat");
    $bilik_penyelaras = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($bilik_penyelaras as $bp) {
        echo "<li><strong>" . htmlspecialchars($bp['nama_bilik_mesyuarat']) . "</strong>";
        if ($bp['penyelaras']) {
            echo " - Penyelaras: " . htmlspecialchars($bp['penyelaras']);
        }
        echo "</li>";
    }
    echo "</ul>";
    
    echo "<h3 style='color: green;'>✓ Setup dengan penyelaras bilik selesai!</h3>";
    
    // Pautan
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='senarai_bilik.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Senarai Bilik</a>";
    echo "<a href='tempah_bilik.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Tempah Bilik</a>";
    echo "<a href='log_masuk.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Database Dengan Penyelaras</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f8f9fa; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h3 { margin-top: 30px; }
        p { margin: 10px 0; }
        ul { background: white; padding: 15px; border-radius: 5px; }
        a { display: inline-block; margin: 5px; }
    </style>
</head>
<body>
</body>
</html>
