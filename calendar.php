<?php
require_once 'config/config.php';
require_once 'classes/Room.php';
require_once 'classes/Booking.php';

requireLogin();

$room = new Room();
$booking = new Booking();

// Get selected month and year
$current_month = (int)($_GET['month'] ?? date('n'));
$current_year = (int)($_GET['year'] ?? date('Y'));

// Validate month and year
if ($current_month < 1 || $current_month > 12) {
    $current_month = date('n');
}
if ($current_year < 2020 || $current_year > 2030) {
    $current_year = date('Y');
}

// Get selected room
$selected_room_id = (int)($_GET['room_id'] ?? 0);
$rooms = $room->getAllRooms();

// Get bookings for the month
$start_date = sprintf('%04d-%02d-01', $current_year, $current_month);
$end_date = date('Y-m-t', strtotime($start_date));

if ($selected_room_id) {
    $month_bookings = $booking->getAllBookings(null, $start_date, $end_date);
    $month_bookings = array_filter($month_bookings, function($b) use ($selected_room_id) {
        return $b['room_id'] == $selected_room_id;
    });
} else {
    $month_bookings = $booking->getAllBookings(null, $start_date, $end_date);
}

// Group bookings by date
$bookings_by_date = [];
foreach ($month_bookings as $booking_item) {
    $date = $booking_item['booking_date'];
    if (!isset($bookings_by_date[$date])) {
        $bookings_by_date[$date] = [];
    }
    $bookings_by_date[$date][] = $booking_item;
}

// Calendar helper functions
function getCalendarDays($year, $month) {
    $first_day = mktime(0, 0, 0, $month, 1, $year);
    $days_in_month = date('t', $first_day);
    $first_day_of_week = date('w', $first_day);
    
    $calendar_days = [];
    
    // Add empty cells for days before the first day of the month
    for ($i = 0; $i < $first_day_of_week; $i++) {
        $calendar_days[] = null;
    }
    
    // Add days of the month
    for ($day = 1; $day <= $days_in_month; $day++) {
        $calendar_days[] = $day;
    }
    
    return $calendar_days;
}

$calendar_days = getCalendarDays($current_year, $current_month);
$month_name = date('F Y', mktime(0, 0, 0, $current_month, 1, $current_year));

$page_title = 'Calendar View';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-calendar"></i> Calendar View</h2>
        <div class="btn-group">
            <a href="rooms.php" class="btn btn-outline-primary">
                <i class="fas fa-door-open"></i> Browse Rooms
            </a>
            <a href="my_bookings.php" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> My Bookings
            </a>
        </div>
    </div>

    <!-- Calendar Controls -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <a href="?month=<?php echo $current_month == 1 ? 12 : $current_month - 1; ?>&year=<?php echo $current_month == 1 ? $current_year - 1 : $current_year; ?>&room_id=<?php echo $selected_room_id; ?>" 
                           class="btn btn-outline-primary me-2">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <h4 class="mb-0 me-2"><?php echo $month_name; ?></h4>
                        <a href="?month=<?php echo $current_month == 12 ? 1 : $current_month + 1; ?>&year=<?php echo $current_month == 12 ? $current_year + 1 : $current_year; ?>&room_id=<?php echo $selected_room_id; ?>" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <form method="GET" class="d-flex">
                        <input type="hidden" name="room_id" value="<?php echo $selected_room_id; ?>">
                        <select name="month" class="form-select me-2">
                            <?php for ($m = 1; $m <= 12; $m++): ?>
                                <option value="<?php echo $m; ?>" <?php echo $m == $current_month ? 'selected' : ''; ?>>
                                    <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                        <select name="year" class="form-select me-2">
                            <?php for ($y = 2020; $y <= 2030; $y++): ?>
                                <option value="<?php echo $y; ?>" <?php echo $y == $current_year ? 'selected' : ''; ?>>
                                    <?php echo $y; ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                        <button type="submit" class="btn btn-primary">Go</button>
                    </form>
                </div>
                <div class="col-md-4">
                    <form method="GET" class="d-flex">
                        <input type="hidden" name="month" value="<?php echo $current_month; ?>">
                        <input type="hidden" name="year" value="<?php echo $current_year; ?>">
                        <select name="room_id" class="form-select me-2">
                            <option value="">All Rooms</option>
                            <?php foreach ($rooms as $room_data): ?>
                                <option value="<?php echo $room_data['id']; ?>" 
                                        <?php echo $room_data['id'] == $selected_room_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($room_data['room_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <button type="submit" class="btn btn-primary">Filter</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Grid -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" style="table-layout: fixed;">
                    <thead class="bg-primary text-white">
                        <tr>
                            <th class="text-center">Sunday</th>
                            <th class="text-center">Monday</th>
                            <th class="text-center">Tuesday</th>
                            <th class="text-center">Wednesday</th>
                            <th class="text-center">Thursday</th>
                            <th class="text-center">Friday</th>
                            <th class="text-center">Saturday</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $week_count = 0;
                        $day_count = 0;
                        
                        foreach (array_chunk($calendar_days, 7) as $week):
                        ?>
                            <tr>
                                <?php foreach ($week as $day): ?>
                                    <td class="p-2" style="height: 120px; vertical-align: top; width: 14.28%;">
                                        <?php if ($day): ?>
                                            <?php
                                            $current_date = sprintf('%04d-%02d-%02d', $current_year, $current_month, $day);
                                            $day_bookings = $bookings_by_date[$current_date] ?? [];
                                            $is_today = $current_date === date('Y-m-d');
                                            $is_past = $current_date < date('Y-m-d');
                                            ?>
                                            
                                            <div class="d-flex justify-content-between align-items-start mb-1">
                                                <span class="fw-bold <?php echo $is_today ? 'text-primary' : ($is_past ? 'text-muted' : ''); ?>">
                                                    <?php echo $day; ?>
                                                </span>
                                                <?php if (!$is_past): ?>
                                                    <a href="rooms.php?date=<?php echo $current_date; ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Book for this date">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <?php if (!empty($day_bookings)): ?>
                                                <div class="booking-list">
                                                    <?php 
                                                    $displayed_bookings = array_slice($day_bookings, 0, 3);
                                                    foreach ($displayed_bookings as $booking_item): 
                                                    ?>
                                                        <div class="booking-item mb-1 p-1 rounded text-white" 
                                                             style="background-color: <?php 
                                                                echo $booking_item['status'] == 'confirmed' ? '#28a745' : 
                                                                    ($booking_item['status'] == 'pending' ? '#ffc107' : 
                                                                    ($booking_item['status'] == 'cancelled' ? '#dc3545' : '#17a2b8')); 
                                                             ?>; font-size: 0.75rem;">
                                                            <div class="fw-bold"><?php echo formatTime($booking_item['start_time']); ?></div>
                                                            <div class="text-truncate" title="<?php echo htmlspecialchars($booking_item['purpose']); ?>">
                                                                <?php echo htmlspecialchars(substr($booking_item['purpose'], 0, 15)); ?>
                                                                <?php if (strlen($booking_item['purpose']) > 15) echo '...'; ?>
                                                            </div>
                                                            <?php if (!$selected_room_id): ?>
                                                                <div class="text-truncate" title="<?php echo htmlspecialchars($booking_item['room_name']); ?>">
                                                                    <small><?php echo htmlspecialchars(substr($booking_item['room_name'], 0, 12)); ?></small>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                    
                                                    <?php if (count($day_bookings) > 3): ?>
                                                        <div class="text-muted small">
                                                            +<?php echo count($day_bookings) - 3; ?> more
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="card mt-3">
        <div class="card-body">
            <h6><i class="fas fa-info-circle"></i> Legend</h6>
            <div class="row">
                <div class="col-md-3">
                    <span class="badge bg-success me-2">■</span> Confirmed
                </div>
                <div class="col-md-3">
                    <span class="badge bg-warning me-2">■</span> Pending
                </div>
                <div class="col-md-3">
                    <span class="badge bg-danger me-2">■</span> Cancelled
                </div>
                <div class="col-md-3">
                    <span class="badge bg-info me-2">■</span> Completed
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.booking-item {
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.booking-item:hover {
    opacity: 0.8;
}

.table td {
    border: 1px solid #dee2e6 !important;
}

.table td:hover {
    background-color: #f8f9fa;
}

@media (max-width: 768px) {
    .table td {
        font-size: 0.8rem;
        padding: 0.5rem !important;
        height: 80px !important;
    }
    
    .booking-item {
        font-size: 0.6rem !important;
        padding: 0.25rem !important;
    }
}
</style>

<script>
// Add click event to booking items
document.addEventListener('DOMContentLoaded', function() {
    const bookingItems = document.querySelectorAll('.booking-item');
    bookingItems.forEach(item => {
        item.addEventListener('click', function() {
            // You can add functionality to show booking details
            // For now, we'll just show an alert
            const purpose = this.getAttribute('title') || this.textContent.trim();
            alert('Booking: ' + purpose);
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
