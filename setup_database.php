<?php
/**
 * Setup Pangkalan Data
 * Sistem Tempahan Bilik Mesyuarat
 * 
 * Fail ini akan mencipta pangkalan data dan jadual yang dip<PERSON>lukan
 */

// Konfigurasi pangkalan data
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    // Sambung ke MySQL tanpa pilih database
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setup Pangkalan Data - Sistem Tempahan Bilik Mesyuarat</h2>";
    
    // Cipta database jika belum wujud
    echo "<p>1. Mencipta pangkalan data '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ Pangkalan data berjaya dicipta/sudah wujud</p>";
    
    // Pilih database
    $pdo->exec("USE $database");
    
    // Baca dan jalankan skrip SQL
    echo "<p>2. Membaca skrip SQL...</p>";
    $sql_file = __DIR__ . '/database/sistem_tempahan_bilik.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("Fail SQL tidak dijumpai: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Pisahkan statement SQL
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^(--|\/\*|\s*$)/', $stmt);
        }
    );

    // Disable foreign key checks untuk membolehkan insert dengan ID yang tidak berturutan
    array_unshift($statements, "SET FOREIGN_KEY_CHECKS = 0");
    array_push($statements, "SET FOREIGN_KEY_CHECKS = 1");
    
    echo "<p>3. Menjalankan " . count($statements) . " statement SQL...</p>";
    
    foreach ($statements as $index => $statement) {
        try {
            $pdo->exec($statement);
            echo "<p style='color: green;'>✓ Statement " . ($index + 1) . " berjaya</p>";
        } catch (PDOException $e) {
            // Abaikan ralat jika jadual sudah wujud
            if (strpos($e->getMessage(), 'already exists') !== false) {
                echo "<p style='color: orange;'>⚠ Statement " . ($index + 1) . " - Jadual sudah wujud</p>";
            } else {
                echo "<p style='color: red;'>✗ Statement " . ($index + 1) . " gagal: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Semak jadual yang dicipta
    echo "<p>4. Menyemak jadual yang dicipta...</p>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li style='color: green;'>✓ $table</li>";
    }
    echo "</ul>";
    
    // Semak data awal
    echo "<p>5. Menyemak data awal...</p>";
    
    // Semak pengguna
    $stmt = $pdo->query("SELECT COUNT(*) FROM pengguna");
    $user_count = $stmt->fetchColumn();
    echo "<p>Jumlah pengguna: $user_count</p>";
    
    // Semak bahagian
    $stmt = $pdo->query("SELECT COUNT(*) FROM bahagian");
    $dept_count = $stmt->fetchColumn();
    echo "<p>Jumlah bahagian: $dept_count</p>";
    
    // Semak bilik
    $stmt = $pdo->query("SELECT COUNT(*) FROM bilik_mesyuarat");
    $room_count = $stmt->fetchColumn();
    echo "<p>Jumlah bilik mesyuarat: $room_count</p>";
    
    // Semak tetapan
    $stmt = $pdo->query("SELECT COUNT(*) FROM tetapan_sistem");
    $setting_count = $stmt->fetchColumn();
    echo "<p>Jumlah tetapan sistem: $setting_count</p>";
    
    echo "<h3 style='color: green;'>✓ Setup pangkalan data selesai!</h3>";
    
    // Papar maklumat login
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Maklumat Login Lalai:</h4>";
    echo "<p><strong>Pentadbir:</strong><br>";
    echo "No. KP: <code>123456789012</code><br>";
    echo "Kata laluan: <code>password</code></p>";
    echo "<p><strong>Penyelaras:</strong><br>";
    echo "No. KP: <code>234567890123</code><br>";
    echo "Kata laluan: <code>password</code></p>";
    echo "<p><strong>Pengguna Biasa:</strong><br>";
    echo "No. KP: <code>345678901234</code><br>";
    echo "Kata laluan: <code>password</code></p>";
    echo "</div>";
    
    // Pautan ke sistem
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='halaman_utama.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Halaman Utama</a>";
    echo "<a href='log_masuk.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk</a>";
    echo "<a href='dashboard.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Dashboard</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat Setup:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>Sila semak:</p>";
    echo "<ul>";
    echo "<li>MySQL server berjalan</li>";
    echo "<li>Kredensial pangkalan data betul</li>";
    echo "<li>Fail SQL wujud di lokasi yang betul</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Pangkalan Data - Sistem Tempahan Bilik Mesyuarat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        h3 {
            margin-top: 30px;
        }
        
        p {
            margin: 10px 0;
        }
        
        ul {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 5px 0;
        }
        
        code {
            background: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        a {
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <!-- Content generated by PHP above -->
</body>
</html>
