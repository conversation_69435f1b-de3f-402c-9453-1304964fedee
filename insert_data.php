<?php
/**
 * Insert Data untuk Sistem Tempahan Bilik Mesyuarat
 * Fail ini akan memasukkan data gred, jawatan, dan bahagian
 */

// Konfigurasi pangkalan data
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    // Sambung ke MySQL
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Insert Data - Sistem Tempahan Bilik Mesyuarat</h2>";
    
    // Baca dan jalankan skrip SQL
    echo "<p>1. Membaca skrip INSERT data...</p>";
    $sql_file = __DIR__ . '/database/insert_data.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("Fail SQL tidak dijumpai: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Pisahkan statement SQL
    $statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^(--|\/\*|\s*$)/', $stmt);
        }
    );
    
    echo "<p>2. Menjalankan " . count($statements) . " statement SQL...</p>";
    
    foreach ($statements as $index => $statement) {
        try {
            $pdo->exec($statement);
            echo "<p style='color: green;'>✓ Statement " . ($index + 1) . " berjaya</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Statement " . ($index + 1) . " gagal: " . $e->getMessage() . "</p>";
        }
    }
    
    // Semak data yang dimasukkan
    echo "<p>3. Menyemak data yang dimasukkan...</p>";
    
    // Semak gred
    $stmt = $pdo->query("SELECT COUNT(*) FROM tgred");
    $gred_count = $stmt->fetchColumn();
    echo "<p>Jumlah gred: $gred_count</p>";
    
    // Semak jawatan
    $stmt = $pdo->query("SELECT COUNT(*) FROM tjawatan");
    $jawatan_count = $stmt->fetchColumn();
    echo "<p>Jumlah jawatan: $jawatan_count</p>";
    
    // Semak bahagian
    $stmt = $pdo->query("SELECT COUNT(*) FROM tbahagian");
    $bahagian_count = $stmt->fetchColumn();
    echo "<p>Jumlah bahagian: $bahagian_count</p>";
    
    // Papar beberapa contoh data
    echo "<h4>Contoh Data Gred:</h4>";
    $stmt = $pdo->query("SELECT * FROM tgred LIMIT 10");
    $gred_sample = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<ul>";
    foreach ($gred_sample as $gred) {
        echo "<li>ID: {$gred['id']} - Gred: {$gred['gred']}</li>";
    }
    echo "</ul>";
    
    echo "<h4>Contoh Data Jawatan:</h4>";
    $stmt = $pdo->query("SELECT * FROM tjawatan LIMIT 10");
    $jawatan_sample = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<ul>";
    foreach ($jawatan_sample as $jawatan) {
        echo "<li>ID: {$jawatan['id']} - Jawatan: {$jawatan['jawatan']}</li>";
    }
    echo "</ul>";
    
    echo "<h4>Data Bahagian:</h4>";
    $stmt = $pdo->query("SELECT * FROM tbahagian");
    $bahagian_all = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<ul>";
    foreach ($bahagian_all as $bahagian) {
        echo "<li>ID: {$bahagian['id']} - Bahagian: {$bahagian['bahagian']}</li>";
    }
    echo "</ul>";
    
    echo "<h3 style='color: green;'>✓ Insert data selesai!</h3>";
    
    // Pautan ke halaman lain
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='setup_database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Setup Database</a>";
    echo "<a href='test_sistem.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Sistem</a>";
    echo "<a href='log_masuk.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat Insert Data:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>Sila semak:</p>";
    echo "<ul>";
    echo "<li>MySQL server berjalan</li>";
    echo "<li>Database 'sistem_tempahan_bilik' wujud</li>";
    echo "<li>Jadual tgred, tjawatan, tbahagian wujud</li>";
    echo "<li>Kredensial pangkalan data betul</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insert Data - Sistem Tempahan Bilik Mesyuarat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        h3 {
            margin-top: 30px;
        }
        
        h4 {
            color: #555;
            margin-top: 20px;
        }
        
        p {
            margin: 10px 0;
        }
        
        ul {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 5px 0;
        }
        
        a {
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <!-- Content generated by PHP above -->
</body>
</html>
