# 🏢 SISTEM TEMPAHAN BILIK MESYUARAT

## ✅ SISTEM LENGKAP DALAM BAHASA MELAYU

Saya telah berjaya membina **Sistem Tempahan Bilik Mesyuarat** yang lengkap dalam Bahasa Melayu dengan ciri-ciri moden dan mesra pengguna.

---

## 📁 STRUKTUR FAIL SISTEM

### 🔧 Konfigurasi & Pangkalan Data
- `config/database.php` - Konfigurasi sambungan pangkalan data
- `config/sistem_config.php` - Konfigurasi utama sistem dalam Bahasa Melayu
- `database/sistem_tempahan_bilik.sql` - Skrip pangkalan data lengkap
- `setup_database.php` - Skrip setup automatik pangkalan data

### 🎨 Template & Layout
- `includes/header_sistem.php` - Header sistem dengan navigasi Bahasa Melayu
- `includes/footer_sistem.php` - Footer sistem dengan maklumat lengkap

### 🔐 Sistem Autentikasi
- `log_masuk.php` - <PERSON><PERSON> log masuk dengan UI menarik
- `daftar.php` - <PERSON><PERSON> pendaftaran pengguna baru
- `log_keluar.php` - Proses log keluar dengan log aktiviti

### 🏠 Halaman Utama & Dashboard
- `halaman_utama.php` - Halaman utama untuk tetamu
- `index_sistem.php` - Halaman indeks yang mengarahkan pengguna
- `dashboard.php` - Dashboard pengguna dengan statistik real-time

### 🏢 Pengurusan Bilik
- `senarai_bilik.php` - Senarai bilik dengan carian dan penapis
- `tempah_bilik.php` - Borang tempahan bilik yang komprehensif

### 📅 Pengurusan Tempahan
- `tempahan_saya.php` - Senarai tempahan pengguna dengan pengurusan
- `kalendar.php` - Kalendar tempahan interaktif

### 🛠️ Utiliti & Ujian
- `test_sistem.php` - Ujian automatik untuk semua fungsi sistem
- `README_SISTEM_MELAYU.md` - Dokumentasi lengkap sistem

---

## 🗄️ STRUKTUR PANGKALAN DATA

### Jadual Utama:
1. **`pengguna`** - Maklumat pengguna dengan peranan
2. **`bahagian`** - Bahagian organisasi
3. **`bilik_mesyuarat`** - Maklumat bilik dengan kemudahan
4. **`tempahan`** - Rekod tempahan dengan status
5. **`sejarah_tempahan`** - Audit trail untuk tempahan
6. **`tetapan_sistem`** - Konfigurasi sistem
7. **`notifikasi`** - Sistem notifikasi dalam aplikasi

### Data Awal:
- **3 pengguna lalai** (pentadbir, penyelaras, pengguna)
- **6 bahagian** organisasi
- **4 bilik mesyuarat** dengan kemudahan lengkap
- **Tetapan sistem** yang boleh dikonfigurasi

---

## ✨ CIRI-CIRI UTAMA

### 🎯 Antara Muka Pengguna
- ✅ **100% Bahasa Melayu** - Semua teks, mesej, dan dokumentasi
- ✅ **Responsive Design** - Sesuai untuk desktop, tablet, dan mobile
- ✅ **Bootstrap 5.3** - UI moden dengan animasi smooth
- ✅ **Bootstrap Icons** - Set ikon yang konsisten
- ✅ **Dark/Light Theme** - Tema yang menarik dengan gradient

### 🔐 Keselamatan
- ✅ **Password Hashing** - Menggunakan PHP `password_hash()`
- ✅ **Session Management** - Pengurusan sesi yang selamat
- ✅ **Input Validation** - Validasi dan sanitasi input
- ✅ **SQL Injection Protection** - Menggunakan prepared statements
- ✅ **XSS Protection** - Perlindungan terhadap serangan XSS

### 👥 Sistem Pengguna
- ✅ **3 Tahap Pengguna** - Pentadbir, Penyelaras, Pengguna
- ✅ **Pendaftaran Automatik** - Borang pendaftaran yang lengkap
- ✅ **Profil Pengguna** - Pengurusan maklumat peribadi
- ✅ **Log Aktiviti** - Jejak audit untuk semua tindakan

### 🏢 Pengurusan Bilik
- ✅ **Senarai Bilik Interaktif** - Grid dan list view
- ✅ **Carian & Penapis** - Cari mengikut nama, lokasi, kapasiti
- ✅ **Maklumat Lengkap** - Kemudahan, gambar, kadar sewa
- ✅ **Status Real-time** - Ketersediaan bilik secara langsung

### 📅 Sistem Tempahan
- ✅ **Borang Tempahan Pintar** - Validasi automatik ketersediaan
- ✅ **Pengiraan Kos** - Kira kos berdasarkan durasi dan kadar
- ✅ **Sistem Kelulusan** - Workflow kelulusan bertingkat
- ✅ **Notifikasi Automatik** - Pemberitahuan status tempahan

### 📊 Dashboard & Laporan
- ✅ **Statistik Real-time** - Tempahan hari ini, bulan ini, dll
- ✅ **Kalendar Visual** - Paparan kalendar interaktif
- ✅ **Carta & Graf** - Visualisasi data penggunaan
- ✅ **Export Data** - Kemudahan export laporan

### 🔔 Sistem Notifikasi
- ✅ **Notifikasi Dalam Aplikasi** - Badge dan dropdown notifikasi
- ✅ **Mesej Flash** - Feedback segera untuk tindakan pengguna
- ✅ **Log Aktiviti** - Rekod semua aktiviti sistem

---

## 🚀 CARA MENGGUNAKAN

### 1. Setup Sistem
```bash
# Akses setup automatik
http://localhost/jkndata/setup_database.php
```

### 2. Ujian Sistem
```bash
# Jalankan ujian automatik
http://localhost/jkndata/test_sistem.php
```

### 3. Akses Sistem
```bash
# Halaman utama
http://localhost/jkndata/halaman_utama.php

# Log masuk
http://localhost/jkndata/log_masuk.php

# Dashboard (selepas log masuk)
http://localhost/jkndata/dashboard.php
```

### 4. Akaun Lalai
**Pentadbir:**
- Nama pengguna: `pentadbir`
- Kata laluan: `password`

**Pengguna Biasa:**
- Nama pengguna: `pengguna1`
- Kata laluan: `password`

---

## 🎨 TEKNOLOGI YANG DIGUNAKAN

### Backend
- **PHP 8.0+** - Bahasa pengaturcaraan utama
- **MySQL 8.0+** - Pangkalan data
- **PDO** - Lapisan abstraksi pangkalan data

### Frontend
- **HTML5** - Struktur halaman
- **CSS3** - Styling dengan custom properties
- **Bootstrap 5.3.0** - Framework CSS
- **Bootstrap Icons 1.10.0** - Set ikon
- **JavaScript ES6+** - Interaktiviti

### Ciri Tambahan
- **Responsive Design** - Mobile-first approach
- **Progressive Enhancement** - Berfungsi tanpa JavaScript
- **Accessibility** - ARIA labels dan semantic HTML
- **SEO Friendly** - Meta tags dan struktur yang betul

---

## 📱 CIRI RESPONSIVE

### Desktop (1200px+)
- Layout 3 kolum untuk dashboard
- Navigasi horizontal penuh
- Modal dan dropdown yang luas

### Tablet (768px - 1199px)
- Layout 2 kolum
- Navigasi collapse
- Touch-friendly buttons

### Mobile (< 768px)
- Layout 1 kolum
- Hamburger menu
- Swipe gestures untuk kalendar

---

## 🔧 KONFIGURASI LANJUTAN

### Tetapan Sistem
Sistem menyokong konfigurasi dinamik melalui jadual `tetapan_sistem`:

- `nama_organisasi` - Nama organisasi
- `masa_tempahan_minimum` - Masa minimum tempahan (minit)
- `masa_tempahan_maksimum` - Masa maksimum tempahan (minit)
- `hari_tempahan_awal` - Berapa hari awal boleh tempah
- `emel_notifikasi` - Aktifkan notifikasi emel
- `kelulusan_automatik` - Tempahan diluluskan automatik

### Customization
- Warna tema boleh diubah dalam CSS variables
- Logo dan branding boleh dikemaskini
- Bahasa boleh ditambah dengan mudah
- Workflow kelulusan boleh disesuaikan

---

## 🎯 KESIMPULAN

Sistem Tempahan Bilik Mesyuarat ini adalah **sistem lengkap dan profesional** yang:

✅ **Sepenuhnya dalam Bahasa Melayu** - Dari UI hingga dokumentasi
✅ **Moden dan Responsif** - Menggunakan teknologi terkini
✅ **Selamat dan Boleh Dipercayai** - Dengan ciri keselamatan yang kukuh
✅ **Mudah Digunakan** - Antara muka yang intuitif
✅ **Boleh Dikembangkan** - Struktur kod yang bersih dan modular
✅ **Siap Untuk Produksi** - Dengan ujian automatik dan dokumentasi lengkap

Sistem ini siap untuk digunakan dalam persekitaran sebenar dan boleh disesuaikan mengikut keperluan organisasi yang berbeza.

---

*Dibangunkan dengan ❤️ menggunakan PHP, MySQL, dan Bootstrap*
*Sistem Tempahan Bilik Mesyuarat v1.0 - 2025*
