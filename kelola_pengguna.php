<?php
/**
 * <PERSON><PERSON><PERSON> Pengguna - Admin
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Semak login dan profile admin
if (!isset($_SESSION['pengguna_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: log_masuk_sebenar.php');
    exit;
}

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

$mesej_ralat = '';
$mesej_kejayaan = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Proses kemaskini profile pengguna
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['kemaskini_profile'])) {
        $pengguna_id = (int)$_POST['pengguna_id'];
        $profile_baru = (int)$_POST['profile_id'];
        
        // Pastikan tidak mengubah profile admin sendiri
        if ($pengguna_id == $_SESSION['pengguna_id']) {
            $mesej_ralat = 'Anda tidak boleh mengubah profile anda sendiri.';
        } else {
            $sql_update = "UPDATE pengguna SET profile_id = ? WHERE id = ?";
            $stmt = $pdo->prepare($sql_update);
            $result = $stmt->execute([$profile_baru, $pengguna_id]);
            
            if ($result) {
                // Dapatkan nama pengguna dan profile
                $sql_info = "SELECT p.nama_penuh, pr.name as profile_name 
                            FROM pengguna p 
                            JOIN profile pr ON p.profile_id = pr.id 
                            WHERE p.id = ?";
                $stmt = $pdo->prepare($sql_info);
                $stmt->execute([$pengguna_id]);
                $info = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $mesej_kejayaan = "Profile {$info['nama_penuh']} berjaya dikemaskini kepada {$info['profile_name']}.";
            } else {
                $mesej_ralat = 'Ralat semasa mengemaskini profile pengguna.';
            }
        }
    }
    
    // Proses aktif/nyahaktif pengguna
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_status'])) {
        $pengguna_id = (int)$_POST['pengguna_id'];
        $status_baru = $_POST['status_baru'];
        
        // Pastikan tidak mengubah status admin sendiri
        if ($pengguna_id == $_SESSION['pengguna_id']) {
            $mesej_ralat = 'Anda tidak boleh mengubah status anda sendiri.';
        } else {
            $sql_update = "UPDATE pengguna SET status = ? WHERE id = ?";
            $stmt = $pdo->prepare($sql_update);
            $result = $stmt->execute([$status_baru, $pengguna_id]);
            
            if ($result) {
                $mesej_kejayaan = "Status pengguna berjaya dikemaskini kepada $status_baru.";
            } else {
                $mesej_ralat = 'Ralat semasa mengemaskini status pengguna.';
            }
        }
    }
    
    // Dapatkan senarai pengguna
    $sql_pengguna = "SELECT p.*, pr.name as profile_name
                     FROM pengguna p
                     JOIN profile pr ON p.profile_id = pr.id
                     ORDER BY p.profile_id, p.nama_penuh";
    
    $stmt = $pdo->prepare($sql_pengguna);
    $stmt->execute();
    $senarai_pengguna = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Dapatkan senarai profile
    $sql_profile = "SELECT * FROM profile ORDER BY id";
    $stmt = $pdo->prepare($sql_profile);
    $stmt->execute();
    $senarai_profile = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Statistik pengguna
    $sql_stats = "SELECT 
                    COUNT(*) as jumlah,
                    COUNT(CASE WHEN profile_id = 1 THEN 1 END) as pengguna,
                    COUNT(CASE WHEN profile_id = 2 THEN 1 END) as admin,
                    COUNT(CASE WHEN profile_id = 3 THEN 1 END) as penyelaras,
                    COUNT(CASE WHEN status = 'aktif' THEN 1 END) as aktif,
                    COUNT(CASE WHEN status = 'tidak_aktif' THEN 1 END) as tidak_aktif
                  FROM pengguna";
    
    $stmt = $pdo->prepare($sql_stats);
    $stmt->execute();
    $statistik = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $mesej_ralat = 'Ralat sistem: ' . $e->getMessage();
    $senarai_pengguna = [];
    $senarai_profile = [];
    $statistik = ['jumlah' => 0, 'pengguna' => 0, 'admin' => 0, 'penyelaras' => 0, 'aktif' => 0, 'tidak_aktif' => 0];
}

function getProfileBadge($profile_id) {
    switch ($profile_id) {
        case 1: return 'bg-primary';
        case 2: return 'bg-danger';
        case 3: return 'bg-warning text-dark';
        case 4: return 'bg-info';
        case 9: return 'bg-secondary';
        default: return 'bg-secondary';
    }
}

function getStatusBadge($status) {
    return $status == 'aktif' ? 'bg-success' : 'bg-secondary';
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Pengguna - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-danger">
        <div class="container">
            <a class="navbar-brand" href="dashboard_admin.php">
                <i class="bi bi-shield-check me-2"></i>Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard_admin.php">
                    <i class="bi bi-house me-1"></i>Dashboard
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i><?= htmlspecialchars($_SESSION['nama_penuh']) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="log_keluar_sebenar.php">
                            <i class="bi bi-box-arrow-right me-2"></i>Log Keluar
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header Halaman -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Kelola Pengguna</h1>
                        <p class="text-muted mb-0">Kelola pengguna sistem dan tukar profile mereka</p>
                    </div>
                    <div>
                        <a href="dashboard_admin.php" class="btn btn-outline-danger">
                            <i class="bi bi-arrow-left me-2"></i>Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mesej -->
        <?php if (!empty($mesej_ralat)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($mesej_kejayaan)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
            </div>
        <?php endif; ?>

        <!-- Statistik Pengguna -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="mb-0"><?= $statistik['jumlah'] ?></h4>
                        <small class="text-muted">Jumlah</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="mb-0 text-danger"><?= $statistik['admin'] ?></h4>
                        <small class="text-muted">Admin</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="mb-0 text-warning"><?= $statistik['penyelaras'] ?></h4>
                        <small class="text-muted">Penyelaras</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="mb-0 text-primary"><?= $statistik['pengguna'] ?></h4>
                        <small class="text-muted">Pengguna</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="mb-0 text-success"><?= $statistik['aktif'] ?></h4>
                        <small class="text-muted">Aktif</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="mb-0 text-secondary"><?= $statistik['tidak_aktif'] ?></h4>
                        <small class="text-muted">Tidak Aktif</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Senarai Pengguna -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-people me-2"></i>Senarai Pengguna</h5>
            </div>
            <div class="card-body">
                <?php if (empty($senarai_pengguna)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-people fs-1 text-muted mb-3"></i>
                        <h4 class="text-muted">Tiada pengguna</h4>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Pengguna</th>
                                    <th>No. KP</th>
                                    <th>Profile</th>
                                    <th>Status</th>
                                    <th>Tarikh Daftar</th>
                                    <th>Tindakan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($senarai_pengguna as $pengguna): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($pengguna['nama_penuh']) ?></strong><br>
                                            <small class="text-muted"><?= htmlspecialchars($pengguna['emel']) ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($pengguna['nokp']) ?></td>
                                        <td>
                                            <span class="badge <?= getProfileBadge($pengguna['profile_id']) ?>">
                                                <?= htmlspecialchars($pengguna['profile_name']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?= getStatusBadge($pengguna['status']) ?>">
                                                <?= ucfirst($pengguna['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('d/m/Y', strtotime($pengguna['tarikh_daftar'])) ?></td>
                                        <td>
                                            <?php if ($pengguna['id'] != $_SESSION['pengguna_id']): ?>
                                                <button type="button" class="btn btn-warning btn-sm me-1" 
                                                        onclick="tukarProfile(<?= $pengguna['id'] ?>, '<?= htmlspecialchars($pengguna['nama_penuh']) ?>', <?= $pengguna['profile_id'] ?>)">
                                                    <i class="bi bi-person-gear"></i>
                                                </button>
                                                <button type="button" class="btn btn-<?= $pengguna['status'] == 'aktif' ? 'secondary' : 'success' ?> btn-sm" 
                                                        onclick="tukarStatus(<?= $pengguna['id'] ?>, '<?= htmlspecialchars($pengguna['nama_penuh']) ?>', '<?= $pengguna['status'] ?>')">
                                                    <i class="bi bi-<?= $pengguna['status'] == 'aktif' ? 'pause' : 'play' ?>"></i>
                                                </button>
                                            <?php else: ?>
                                                <small class="text-muted">Anda</small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal Tukar Profile -->
    <div class="modal fade" id="modalTukarProfile" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="">
                    <div class="modal-header">
                        <h5 class="modal-title">Tukar Profile Pengguna</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="kemaskini_profile" value="1">
                        <input type="hidden" name="pengguna_id" id="pengguna_id">
                        
                        <div class="mb-3">
                            <label class="form-label">Pengguna:</label>
                            <p class="fw-bold" id="nama_pengguna"></p>
                        </div>
                        
                        <div class="mb-3">
                            <label for="profile_id" class="form-label">Profile Baru:</label>
                            <select class="form-select" name="profile_id" id="profile_id" required>
                                <?php foreach ($senarai_profile as $profile): ?>
                                    <option value="<?= $profile['id'] ?>"><?= htmlspecialchars($profile['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Perubahan profile akan mengubah akses pengguna dalam sistem.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-warning">Tukar Profile</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Tukar Status -->
    <div class="modal fade" id="modalTukarStatus" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="">
                    <div class="modal-header">
                        <h5 class="modal-title">Tukar Status Pengguna</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="toggle_status" value="1">
                        <input type="hidden" name="pengguna_id" id="status_pengguna_id">
                        <input type="hidden" name="status_baru" id="status_baru">
                        
                        <div class="mb-3">
                            <label class="form-label">Pengguna:</label>
                            <p class="fw-bold" id="status_nama_pengguna"></p>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <span id="pesan_status"></span>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn" id="btn_status">Sahkan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function tukarProfile(id, nama, profile_semasa) {
            document.getElementById('pengguna_id').value = id;
            document.getElementById('nama_pengguna').textContent = nama;
            document.getElementById('profile_id').value = profile_semasa;
            
            new bootstrap.Modal(document.getElementById('modalTukarProfile')).show();
        }
        
        function tukarStatus(id, nama, status_semasa) {
            document.getElementById('status_pengguna_id').value = id;
            document.getElementById('status_nama_pengguna').textContent = nama;
            
            const status_baru = status_semasa === 'aktif' ? 'tidak_aktif' : 'aktif';
            document.getElementById('status_baru').value = status_baru;
            
            const pesan = document.getElementById('pesan_status');
            const btn = document.getElementById('btn_status');
            
            if (status_baru === 'tidak_aktif') {
                pesan.textContent = 'Pengguna akan dinyahaktifkan dan tidak boleh log masuk.';
                btn.textContent = 'Nyahaktif';
                btn.className = 'btn btn-danger';
            } else {
                pesan.textContent = 'Pengguna akan diaktifkan dan boleh log masuk.';
                btn.textContent = 'Aktifkan';
                btn.className = 'btn btn-success';
            }
            
            new bootstrap.Modal(document.getElementById('modalTukarStatus')).show();
        }
    </script>
</body>
</html>
