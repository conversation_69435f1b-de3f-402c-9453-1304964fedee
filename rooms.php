<?php
require_once 'config/config.php';
require_once 'classes/Room.php';

requireLogin();

$room = new Room();
$rooms = $room->getAllRooms();

// Handle search
$search_term = sanitizeInput($_GET['search'] ?? '');
$capacity_filter = (int)($_GET['capacity'] ?? 0);
$location_filter = sanitizeInput($_GET['location'] ?? '');

if ($search_term || $capacity_filter || $location_filter) {
    $rooms = $room->searchRooms($search_term, $capacity_filter ?: null, $location_filter ?: null);
}

$page_title = 'Meeting Rooms';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter"></i> Filter Rooms</h5>
                </div>
                <div class="card-body">
                    <form method="GET">
                        <div class="mb-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search_term); ?>" 
                                   placeholder="Room name or description">
                        </div>
                        
                        <div class="mb-3">
                            <label for="capacity" class="form-label">Minimum Capacity</label>
                            <input type="number" class="form-control" id="capacity" name="capacity" 
                                   value="<?php echo $capacity_filter ?: ''; ?>" min="1">
                        </div>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="<?php echo htmlspecialchars($location_filter); ?>" 
                                   placeholder="Floor, wing, etc.">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="rooms.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-door-open"></i> Meeting Rooms</h2>
                <?php if (isAdmin()): ?>
                    <a href="admin/rooms.php" class="btn btn-success">
                        <i class="fas fa-plus"></i> Manage Rooms
                    </a>
                <?php endif; ?>
            </div>

            <?php if (empty($rooms)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No rooms found matching your criteria.
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($rooms as $room_data): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <?php if ($room_data['image_url']): ?>
                                    <img src="<?php echo htmlspecialchars($room_data['image_url']); ?>" 
                                         class="card-img-top" style="height: 200px; object-fit: cover;" 
                                         alt="<?php echo htmlspecialchars($room_data['room_name']); ?>">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                         style="height: 200px;">
                                        <i class="fas fa-door-open fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($room_data['room_name']); ?></h5>
                                    <p class="card-text text-muted">
                                        <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($room_data['location']); ?>
                                    </p>
                                    <p class="card-text"><?php echo htmlspecialchars($room_data['description']); ?></p>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Capacity</small><br>
                                            <strong><i class="fas fa-users"></i> <?php echo $room_data['capacity']; ?></strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Rate/Hour</small><br>
                                            <strong><?php echo formatCurrency($room_data['hourly_rate']); ?></strong>
                                        </div>
                                    </div>
                                    
                                    <?php 
                                    $facilities = json_decode($room_data['facilities'], true) ?: [];
                                    if (!empty($facilities)): 
                                    ?>
                                        <div class="mb-3">
                                            <small class="text-muted">Facilities:</small><br>
                                            <?php foreach (array_slice($facilities, 0, 3) as $facility): ?>
                                                <span class="badge bg-secondary me-1"><?php echo htmlspecialchars($facility); ?></span>
                                            <?php endforeach; ?>
                                            <?php if (count($facilities) > 3): ?>
                                                <span class="badge bg-light text-dark">+<?php echo count($facilities) - 3; ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="card-footer">
                                    <div class="d-grid gap-2">
                                        <a href="room_details.php?id=<?php echo $room_data['id']; ?>" 
                                           class="btn btn-outline-primary">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                        <a href="book_room.php?room_id=<?php echo $room_data['id']; ?>" 
                                           class="btn btn-primary">
                                            <i class="fas fa-calendar-plus"></i> Book Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
