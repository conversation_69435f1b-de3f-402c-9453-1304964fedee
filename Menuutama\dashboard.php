<?php
/**
 * Dashboard Pengguna - Sistem Sebenar
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Semak login
if (!isset($_SESSION['pengguna_id'])) {
    header('Location: log_masuk_sebenar.php');
    exit;
}

// Redirect penyelaras ke halaman mereka
if (isset($_SESSION['profile_id']) && $_SESSION['profile_id'] == 3) {
    header('Location: senarai_tempahan_penyelaras.php');
    exit;
}

// Redirect admin ke halaman mereka
if (isset($_SESSION['profile_id']) && $_SESSION['profile_id'] == 2) {
    header('Location: dashboard_admin.php');
    exit;
}

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Dapatkan tempahan pengguna
    $sql_tempahan = "SELECT t.*, b.nama_bilik_mesyuarat, b.kapasiti
                     FROM ttempahan t
                     JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
                     WHERE t.idpemohon = ? AND t.batal_tempahan != 'BATAL'
                     ORDER BY t.tarikh_mula DESC
                     LIMIT 5";
    
    $stmt = $pdo->prepare($sql_tempahan);
    $stmt->execute([$_SESSION['pengguna_id']]);
    $tempahan_terkini = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Statistik tempahan pengguna
    $sql_stats = "SELECT 
                    COUNT(*) as jumlah_tempahan,
                    COUNT(CASE WHEN t.kelulusan = 'MENUNGGU' THEN 1 END) as menunggu,
                    COUNT(CASE WHEN t.kelulusan = 'LULUS' THEN 1 END) as diluluskan,
                    COUNT(CASE WHEN t.kelulusan = 'TOLAK' THEN 1 END) as ditolak
                  FROM ttempahan t
                  WHERE t.idpemohon = ? AND t.batal_tempahan != 'BATAL'";
    
    $stmt = $pdo->prepare($sql_stats);
    $stmt->execute([$_SESSION['pengguna_id']]);
    $statistik = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Dapatkan senarai bilik tersedia
    $sql_bilik = "SELECT COUNT(*) as jumlah_bilik FROM tbilik_mesyuarat WHERE status = 'tersedia'";
    $stmt = $pdo->prepare($sql_bilik);
    $stmt->execute();
    $jumlah_bilik = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $tempahan_terkini = [];
    $statistik = ['jumlah_tempahan' => 0, 'menunggu' => 0, 'diluluskan' => 0, 'ditolak' => 0];
    $jumlah_bilik = 0;
}

function formatTarikhMasa($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}

function getBadgeClass($status) {
    switch ($status) {
        case 'MENUNGGU': return 'bg-warning text-dark';
        case 'LULUS': return 'bg-success';
        case 'TOLAK': return 'bg-danger';
        default: return 'bg-secondary';
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Sistem Tempahan Bilik Mesyuarat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building me-2"></i>Sistem Tempahan Bilik
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i><?= htmlspecialchars($_SESSION['nama_penuh']) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><span class="dropdown-header">
                            <strong><?= htmlspecialchars($_SESSION['nama_penuh']) ?></strong><br>
                            <small><?= htmlspecialchars($_SESSION['profile_name']) ?></small>
                        </span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="tempahan_saya.php">
                            <i class="bi bi-calendar-check me-2"></i>Tempahan Saya
                        </a></li>
                        <li><a class="dropdown-item" href="log_keluar.php">
                            <i class="bi bi-box-arrow-right me-2"></i>Log Keluar
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header Halaman -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Dashboard</h1>
                        <p class="text-muted mb-0">Selamat datang, <?= htmlspecialchars($_SESSION['nama_penuh']) ?></p>
                    </div>
                    <div>
                        <a href="tempah_bilik_sebenar.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Tempah Bilik
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistik -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-building fs-1 text-info mb-2"></i>
                        <h3 class="mb-0"><?= $jumlah_bilik ?></h3>
                        <p class="text-muted mb-0">Bilik Tersedia</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-calendar-check fs-1 text-primary mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['jumlah_tempahan'] ?></h3>
                        <p class="text-muted mb-0">Jumlah Tempahan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-clock-history fs-1 text-warning mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['menunggu'] ?></h3>
                        <p class="text-muted mb-0">Menunggu</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-check-circle fs-1 text-success mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['diluluskan'] ?></h3>
                        <p class="text-muted mb-0">Diluluskan</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu Utama -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-plus fs-1 text-primary mb-3"></i>
                        <h5 class="card-title">Tempah Bilik</h5>
                        <p class="card-text">Buat tempahan bilik mesyuarat untuk acara anda.</p>
                        <a href="tempah_bilik_sebenar.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Tempah Sekarang
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-list-check fs-1 text-success mb-3"></i>
                        <h5 class="card-title">Tempahan Saya</h5>
                        <p class="card-text">Lihat dan kelola tempahan yang telah anda buat.</p>
                        <a href="tempahan_saya.php" class="btn btn-success">
                            <i class="bi bi-calendar-check me-2"></i>Lihat Tempahan
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-building fs-1 text-info mb-3"></i>
                        <h5 class="card-title">Senarai Bilik</h5>
                        <p class="card-text">Lihat senarai bilik mesyuarat yang tersedia.</p>
                        <a href="senarai_bilik.php" class="btn btn-info">
                            <i class="bi bi-search me-2"></i>Lihat Bilik
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tempahan Terkini -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Tempahan Terkini</h5>
            </div>
            <div class="card-body">
                <?php if (empty($tempahan_terkini)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-calendar-x fs-1 text-muted mb-3"></i>
                        <h4 class="text-muted">Belum ada tempahan</h4>
                        <p class="text-muted">Anda belum membuat sebarang tempahan.</p>
                        <a href="tempah_bilik_sebenar.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Buat Tempahan Pertama
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Tajuk Mesyuarat</th>
                                    <th>Bilik</th>
                                    <th>Tarikh & Masa</th>
                                    <th>Peserta</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tempahan_terkini as $tempahan): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?></strong><br>
                                            <small class="text-muted">
                                                Pengerusi: <?= htmlspecialchars($tempahan['pengerusi']) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($tempahan['nama_bilik_mesyuarat']) ?><br>
                                            <small class="text-muted">Kapasiti: <?= $tempahan['kapasiti'] ?> orang</small>
                                        </td>
                                        <td>
                                            <strong><?= formatTarikhMasa($tempahan['tarikh_mula']) ?></strong><br>
                                            <small class="text-muted">hingga <?= formatTarikhMasa($tempahan['tarikh_tamat']) ?></small>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info"><?= $tempahan['bilangan_peserta'] ?> orang</span>
                                        </td>
                                        <td>
                                            <span class="badge <?= getBadgeClass($tempahan['kelulusan']) ?>">
                                                <?= $tempahan['kelulusan'] ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="tempahan_saya.php" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-right me-2"></i>Lihat Semua Tempahan
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
