<?php
/**
 * Halaman Profil Pengguna
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Profil Saya';
require_once 'config/sistem_config.php';

// Semak login
perluLogin();

$mesej_ralat = '';
$mesej_kejayaan = '';

// Dapatkan maklumat pengguna lengkap
try {
    $sql = "SELECT p.*, b.bahagian, j.jawatan, g.gred
            FROM pengguna p
            LEFT JOIN tbahagian b ON p.bahagian_id = b.id
            LEFT JOIN tjawatan j ON p.jawatan_id = j.id
            LEFT JOIN tgred g ON p.gred_id = g.id
            WHERE p.id = ?";
    $pengguna = $db->fetch($sql, [$_SESSION['pengguna_id']]);
    
    if (!$pengguna) {
        $mesej_ralat = 'Maklumat pengguna tidak dijumpai.';
    }
} catch (Exception $e) {
    $mesej_ralat = 'Ralat memuatkan maklumat pengguna: ' . $e->getMessage();
}

// Proses kemaskini profil
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['kemaskini_profil'])) {
    $nama_penuh = bersihkanInput($_POST['nama_penuh']);
    $emel = bersihkanInput($_POST['emel']);
    $no_telefon = bersihkanInput($_POST['no_telefon']);
    $bahagian_id = (int)$_POST['bahagian_id'];
    $jawatan_id = (int)$_POST['jawatan_id'];
    $gred_id = (int)$_POST['gred_id'];
    
    // Validasi input
    if (empty($nama_penuh) || empty($emel)) {
        $mesej_ralat = 'Nama penuh dan emel adalah wajib.';
    } elseif (!filter_var($emel, FILTER_VALIDATE_EMAIL)) {
        $mesej_ralat = 'Format emel tidak sah.';
    } else {
        try {
            // Semak jika emel sudah digunakan oleh pengguna lain
            $sql_check = "SELECT id FROM pengguna WHERE emel = ? AND id != ?";
            $existing = $db->fetch($sql_check, [$emel, $_SESSION['pengguna_id']]);
            
            if ($existing) {
                $mesej_ralat = 'Emel sudah digunakan oleh pengguna lain.';
            } else {
                // Kemaskini maklumat pengguna
                $sql_update = "UPDATE pengguna SET 
                               nama_penuh = ?, emel = ?, no_telefon = ?, 
                               bahagian_id = ?, jawatan_id = ?, gred_id = ?
                               WHERE id = ?";
                
                $result = $db->query($sql_update, [
                    $nama_penuh, $emel, $no_telefon, 
                    $bahagian_id, $jawatan_id, $gred_id, 
                    $_SESSION['pengguna_id']
                ]);
                
                if ($result) {
                    // Kemaskini session
                    $_SESSION['nama_penuh'] = $nama_penuh;
                    $_SESSION['emel'] = $emel;
                    
                    // Dapatkan nama bahagian, jawatan, gred yang baru
                    $sql_new = "SELECT b.bahagian, j.jawatan, g.gred
                                FROM pengguna p
                                LEFT JOIN tbahagian b ON p.bahagian_id = b.id
                                LEFT JOIN tjawatan j ON p.jawatan_id = j.id
                                LEFT JOIN tgred g ON p.gred_id = g.id
                                WHERE p.id = ?";
                    $new_data = $db->fetch($sql_new, [$_SESSION['pengguna_id']]);
                    
                    $_SESSION['bahagian'] = $new_data['bahagian'];
                    $_SESSION['jawatan'] = $new_data['jawatan'];
                    $_SESSION['gred'] = $new_data['gred'];
                    
                    // Log aktiviti
                    logAktiviti($_SESSION['pengguna_id'], 'Kemaskini Profil', 'Pengguna mengemaskini maklumat profil');
                    
                    $mesej_kejayaan = 'Profil berjaya dikemaskini.';
                    
                    // Refresh data pengguna
                    $pengguna = $db->fetch($sql, [$_SESSION['pengguna_id']]);
                } else {
                    $mesej_ralat = 'Ralat semasa mengemaskini profil.';
                }
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat sistem: ' . $e->getMessage();
        }
    }
}

// Dapatkan senarai untuk dropdown
try {
    $sql_bahagian = "SELECT id, bahagian FROM tbahagian ORDER BY bahagian";
    $senarai_bahagian = $db->fetchAll($sql_bahagian);
    
    $sql_jawatan = "SELECT id, jawatan FROM tjawatan ORDER BY jawatan";
    $senarai_jawatan = $db->fetchAll($sql_jawatan);
    
    $sql_gred = "SELECT id, gred FROM tgred ORDER BY gred";
    $senarai_gred = $db->fetchAll($sql_gred);
} catch (Exception $e) {
    $senarai_bahagian = [];
    $senarai_jawatan = [];
    $senarai_gred = [];
}

require_once 'includes/header_sistem.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Profil Saya</h1>
                <p class="text-muted mb-0">Lihat dan kemaskini maklumat profil anda</p>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($mesej_ralat)): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
    </div>
<?php endif; ?>

<?php if (!empty($mesej_kejayaan)): ?>
    <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-gear me-2"></i>Kemaskini Profil</h5>
            </div>
            <div class="card-body">
                <?php if ($pengguna): ?>
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nokp" class="form-label">No. Kad Pengenalan</label>
                            <input type="text" class="form-control" id="nokp" 
                                   value="<?= htmlspecialchars($pengguna['nokp']) ?>" readonly>
                            <small class="text-muted">No. KP tidak boleh diubah</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="nama_penuh" class="form-label">Nama Penuh *</label>
                            <input type="text" class="form-control" id="nama_penuh" name="nama_penuh" 
                                   value="<?= htmlspecialchars($pengguna['nama_penuh']) ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="emel" class="form-label">Emel *</label>
                            <input type="email" class="form-control" id="emel" name="emel" 
                                   value="<?= htmlspecialchars($pengguna['emel']) ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="no_telefon" class="form-label">No. Telefon</label>
                            <input type="tel" class="form-control" id="no_telefon" name="no_telefon" 
                                   value="<?= htmlspecialchars($pengguna['no_telefon'] ?? '') ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="bahagian_id" class="form-label">Bahagian</label>
                            <select class="form-select" id="bahagian_id" name="bahagian_id">
                                <option value="">Pilih Bahagian</option>
                                <?php foreach ($senarai_bahagian as $bahagian): ?>
                                    <option value="<?= $bahagian['id'] ?>" 
                                            <?= ($pengguna['bahagian_id'] == $bahagian['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($bahagian['bahagian']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="jawatan_id" class="form-label">Jawatan</label>
                            <select class="form-select" id="jawatan_id" name="jawatan_id">
                                <option value="">Pilih Jawatan</option>
                                <?php foreach ($senarai_jawatan as $jawatan): ?>
                                    <option value="<?= $jawatan['id'] ?>" 
                                            <?= ($pengguna['jawatan_id'] == $jawatan['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($jawatan['jawatan']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="gred_id" class="form-label">Gred</label>
                            <select class="form-select" id="gred_id" name="gred_id">
                                <option value="">Pilih Gred</option>
                                <?php foreach ($senarai_gred as $gred): ?>
                                    <option value="<?= $gred['id'] ?>" 
                                            <?= ($pengguna['gred_id'] == $gred['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($gred['gred']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="peranan" class="form-label">Peranan</label>
                            <input type="text" class="form-control" id="peranan" 
                                   value="<?= ucfirst($pengguna['peranan']) ?>" readonly>
                            <small class="text-muted">Peranan ditetapkan oleh pentadbir</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <input type="text" class="form-control" id="status" 
                                   value="<?= ucfirst($pengguna['status']) ?>" readonly>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" name="kemaskini_profil" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Kemaskini Profil
                        </button>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Maklumat Akaun -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Maklumat Akaun</h6>
            </div>
            <div class="card-body">
                <?php if ($pengguna): ?>
                <table class="table table-sm">
                    <tr>
                        <td><strong>No. KP:</strong></td>
                        <td><?= htmlspecialchars($pengguna['nokp']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Nama:</strong></td>
                        <td><?= htmlspecialchars($pengguna['nama_penuh']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Bahagian:</strong></td>
                        <td><?= htmlspecialchars($pengguna['bahagian'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Jawatan:</strong></td>
                        <td><?= htmlspecialchars($pengguna['jawatan'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Gred:</strong></td>
                        <td><?= htmlspecialchars($pengguna['gred'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Peranan:</strong></td>
                        <td>
                            <span class="badge bg-primary"><?= ucfirst($pengguna['peranan']) ?></span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge <?= ($pengguna['status'] == 'aktif') ? 'bg-success' : 'bg-secondary' ?>">
                                <?= ucfirst($pengguna['status']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Tarikh Daftar:</strong></td>
                        <td><?= formatTarikhMasa($pengguna['tarikh_daftar']) ?></td>
                    </tr>
                </table>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Pautan Berguna -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-link me-2"></i>Pautan Berguna</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="tukar_kata_laluan.php" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-key me-2"></i>Tukar Kata Laluan
                    </a>
                    <a href="tempahan_saya.php" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-check me-2"></i>Tempahan Saya
                    </a>
                    <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-house me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer_sistem.php'; ?>
