<?php
/**
 * Setup Database Dengan Unit
 * Sistem Tempahan Bilik Mesyuarat
 */

// Konfigurasi pangkalan data
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    // Sambung ke MySQL tanpa pilih database
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setup Database Dengan Unit - Sistem Tempahan Bilik Mesyuarat</h2>";
    
    // Cipta database jika belum wujud
    echo "<p>1. Mencipta pangkalan data '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ Pangkalan data berjaya dicipta/sudah wujud</p>";
    
    // Pilih database
    $pdo->exec("USE $database");
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    echo "<p>2. Mencipta jadual...</p>";
    
    // Drop dan cipta jadual mengikut urutan
    $tables_sql = [
        // Jadual Gred
        "DROP TABLE IF EXISTS `tgred`",
        "CREATE TABLE `tgred` (
            `id` int(11) NOT NULL,
            `gred` varchar(10) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Jawatan
        "DROP TABLE IF EXISTS `tjawatan`",
        "CREATE TABLE `tjawatan` (
            `id` int(100) NOT NULL,
            `jawatan` varchar(100) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Bahagian
        "DROP TABLE IF EXISTS `tbahagian`",
        "CREATE TABLE `tbahagian` (
            `id` int(6) NOT NULL,
            `bahagian` varchar(100) DEFAULT NULL,
            `idptj` int(6) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Unit
        "DROP TABLE IF EXISTS `tunit`",
        "CREATE TABLE `tunit` (
            `id` int(6) NOT NULL,
            `unit` varchar(100) DEFAULT NULL,
            `idbahagian` int(6) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `fk_unit_bahagian` (`idbahagian`),
            CONSTRAINT `fk_unit_bahagian` FOREIGN KEY (`idbahagian`) REFERENCES `tbahagian` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=latin1",
        
        // Jadual Pengguna
        "DROP TABLE IF EXISTS `pengguna`",
        "CREATE TABLE pengguna (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nokp VARCHAR(12) UNIQUE NOT NULL,
            kata_laluan VARCHAR(255) NOT NULL,
            nama_penuh VARCHAR(100) NOT NULL,
            emel VARCHAR(100) UNIQUE NOT NULL,
            no_telefon VARCHAR(20),
            bahagian_id INT,
            unit_id INT,
            jawatan_id INT,
            gred_id INT,
            peranan ENUM('pentadbir', 'pengguna', 'penyelaras') DEFAULT 'pengguna',
            status ENUM('aktif', 'tidak_aktif') DEFAULT 'aktif',
            tarikh_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (bahagian_id) REFERENCES tbahagian(id) ON DELETE SET NULL,
            FOREIGN KEY (unit_id) REFERENCES tunit(id) ON DELETE SET NULL,
            FOREIGN KEY (jawatan_id) REFERENCES tjawatan(id) ON DELETE SET NULL,
            FOREIGN KEY (gred_id) REFERENCES tgred(id) ON DELETE SET NULL
        )"
    ];
    
    foreach ($tables_sql as $sql) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✓ SQL executed successfully</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ SQL failed: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p>3. Memasukkan data...</p>";
    
    // Insert data bahagian
    $pdo->exec("INSERT INTO `tbahagian`(`id`,`bahagian`,`idptj`) VALUES 
        (1,'Kesihatan Awam',NULL),
        (2,'Perubatan',NULL),
        (3,'Pengurusan',NULL),
        (4,'Pergigian',NULL),
        (5,'Farmasi',NULL),
        (6,'Keselamatan & Kualiti Makanan',NULL)");
    echo "<p style='color: green;'>✓ Data bahagian dimasukkan</p>";
    
    // Insert data unit
    $pdo->exec("INSERT INTO `tunit`(`id`,`unit`,`idbahagian`) VALUES 
        (2,'ALAM SEKITAR',1),
        (3,'INSPEKTORAT & PERUNDANGAN',1),
        (4,'KEJURUTERAAN',1),
        (5,'KESIHATAN AWAM',1),
        (6,'KESIHATAN KELUARGA',1),
        (7,'KESIHATAN PRIMER',1),
        (8,'KPAS',1),
        (9,'PEMAKANAN',1),
        (10,'PEMBANGUNAN KELUARGA',1),
        (11,'PENYAKIT BERJANGKIT',1),
        (12,'PENYAKIT TIDAK BERJANGKIT',1),
        (13,'PROMOSI KESIHATAN',1),
        (14,'TPKN(KA)',1),
        (15,'Pengurusan Perubatan',2),
        (17,'KAUNSELING',3),
        (18,'KEJURURAWATAN',3),
        (19,'KEWANGAN',3),
        (20,'LATIHAN',3),
        (21,'PEMBANGUNAN',3),
        (22,'PENGARAH',3),
        (23,'KHIDMAT PENGURUSAN',3),
        (24,'PERJAWATAN',3),
        (25,'SM',3),
        (26,'TPKN(U)',3),
        (31,'Amalan & Perkembangan Farmasi',5),
        (32,'Pengurusan Farmasi',5),
        (33,'Cawangan Penguatkuasaan Farmasi',5),
        (34,'BKKM',6),
        (54,'ICT',3),
        (55,'UKAPS',2),
        (56,'Kejuruteraan',2),
        (57,'Rekod Perubatan',2),
        (58,'Pengurusan Pergigian',4),
        (59,'Kualiti',2),
        (60,'Unit PPP',2),
        (61,'HIV/STI',1),
        (62,'PEROLEHAN DAN ASET',3)");
    echo "<p style='color: green;'>✓ Data unit dimasukkan</p>";
    
    // Insert data gred (sebahagian)
    $gred_values = [];
    $gred_data = [
        [1,'B22'], [2,'C27'], [3,'C32'], [4,'C41'], [5,'C44'], [6,'C48'], [7,'C52'], 
        [8,'F29'], [9,'F32'], [10,'F41'], [11,'F44'], [12,'FT17'], [13,'J17'], [14,'J29'], 
        [15,'J41'], [16,'J44'], [17,'J48'], [18,'KP17'], [19,'M41'], [20,'M44'], [21,'M48'], 
        [22,'M52'], [23,'N1'], [24,'N17'], [25,'N22'], [26,'N26'], [27,'N27'], [28,'N28'], 
        [29,'N32'], [30,'N36'], [31,'N4'], [32,'N41'], [33,'R1'], [34,'R3'], [35,'R4'], 
        [36,'R6'], [37,'S41'], [38,'S44'], [39,'S48'], [40,'U17'], [41,'U29'], [42,'U32'], 
        [43,'U36'], [44,'U38'], [45,'U41'], [46,'U42'], [47,'U44'], [48,'U48'], [49,'U52'], 
        [50,'U54'], [51,'UD44'], [52,'UD48'], [53,'UD51'], [54,'UD52'], [55,'UD54'], 
        [56,'W17'], [57,'W22'], [58,'W27'], [59,'W36'], [60,'W44'], [62,'M48'], [63,'M54'], 
        [64,'H11'], [65,'N11'], [66,'R11']
    ];
    
    foreach ($gred_data as $gred) {
        $gred_values[] = "({$gred[0]},'{$gred[1]}')";
    }
    
    $pdo->exec("INSERT INTO `tgred`(`id`,`gred`) VALUES " . implode(',', $gred_values));
    echo "<p style='color: green;'>✓ Data gred dimasukkan</p>";
    
    // Insert data jawatan (sebahagian)
    $jawatan_data = [
        [2,'JURUAUDIO VISUAL'], [3,'JURURAWAT'], [4,'JURURAWAT PERGIGIAN'], [5,'JURUTEKNIK'],
        [6,'JURUTEKNIK KOMPUTER'], [7,'JURUTEKNOLOGI MAKMAL PERUBATAN'], [8,'JURUTERA (AWAM)'],
        [9,'JURUTERA (ELEKTRIK)'], [10,'JURUTERA (KESIHATAN UMUM)'], [11,'JURUTERA (MEKANIKAL)'],
        [12,'PEGAWAI FARMASI'], [14,'PEGAWAI KAUNSELOR'], [15,'PEGAWAI KESIHATAN PERSEKITARAN'],
        [16,'PEGAWAI KHIDMAT PELANGGAN'], [18,'PEGAWAI PERGIGIAN'], [19,'PEGAWAI PERGIGIAN'],
        [20,'PEGAWAI PERUBATAN'], [22,'PEGAWAI SAINS'], [23,'PEGAWAI SAINS (KIMIA HAYAT)'],
        [24,'PEGAWAI SAINS (PEGAWAI ZAT MAKANAN)'], [26,'PEGAWAI TADBIR DAN DIPLOMATIK'],
        [27,'PEGAWAI TEKNOLOGI MAKANAN'], [28,'PEGAWAI TEKNOLOGI MAKLUMAT'], [29,'PEKERJA AWAM'],
        [30,'PEKERJA RENDAH AWAM'], [31,'PEMANDU KENDERAAN'], [32,'PEMBANTU AM PEJABAT'],
        [33,'PEMBANTU KESELAMATAN'], [34,'PEMBANTU KESIHATAN AWAM'], [35,'PEMBANTU TADBIR (KESETIAUSAHAAN)'],
        [36,'PEMBANTU TADBIR (KEWANGAN)'], [37,'PEMBANTU TADBIR (P/O)'], [39,'PEMBANTU TEKNIK'],
        [40,'PEN. PEG. TEKNOLOGI MAKANAN'], [41,'PENOLONG AKAUNTAN'], [42,'PENOLONG JURUTERA'],
        [43,'PENOLONG PEGAWAI KESIHATAN PERSEKITARAN'], [44,'PENOLONG PEGAWAI PERUBATAN'],
        [45,'PENOLONG PEGAWAI SAINS'], [46,'PENOLONG PEGAWAI TADBIR'], [47,'PEN. PEGAWAI TADBIR (REKOD PERUBATAN)'],
        [48,'PEN. PEGAWAI TEKNOLOGI MAKLUMAT'], [49,'PEREKA'], [50,'SETIAUSAHA PEJABAT'],
        [52,'TIMB. PENGARAH KESIHATAN NEGERI (PENGURUSAN)'], [53,'PENGARAH KESIHATAN NEGERI'], [54,'PENGARAH HOSPITAL']
    ];
    
    $jawatan_values = [];
    foreach ($jawatan_data as $jawatan) {
        $jawatan_values[] = "({$jawatan[0]},'{$jawatan[1]}')";
    }
    
    $pdo->exec("INSERT INTO `tjawatan`(`id`,`jawatan`) VALUES " . implode(',', $jawatan_values));
    echo "<p style='color: green;'>✓ Data jawatan dimasukkan</p>";
    
    // Insert pengguna lalai
    $pdo->exec("INSERT INTO pengguna (nokp, kata_laluan, nama_penuh, emel, peranan, bahagian_id, unit_id, jawatan_id, gred_id) VALUES
        ('123456789012', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Pentadbir Sistem', '<EMAIL>', 'pentadbir', 3, 22, 26, 32),
        ('234567890123', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. Ahmad bin Ali', '<EMAIL>', 'penyelaras', 2, 15, 20, 45),
        ('345678901234', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Siti binti Hassan', '<EMAIL>', 'pengguna', 1, 5, 3, 47)");
    echo "<p style='color: green;'>✓ Data pengguna lalai dimasukkan</p>";
    
    // Enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Semak data
    echo "<p>4. Menyemak data...</p>";
    $bahagian_count = $pdo->query("SELECT COUNT(*) FROM tbahagian")->fetchColumn();
    $unit_count = $pdo->query("SELECT COUNT(*) FROM tunit")->fetchColumn();
    $jawatan_count = $pdo->query("SELECT COUNT(*) FROM tjawatan")->fetchColumn();
    $gred_count = $pdo->query("SELECT COUNT(*) FROM tgred")->fetchColumn();
    $pengguna_count = $pdo->query("SELECT COUNT(*) FROM pengguna")->fetchColumn();
    
    echo "<p>Bahagian: $bahagian_count</p>";
    echo "<p>Unit: $unit_count</p>";
    echo "<p>Jawatan: $jawatan_count</p>";
    echo "<p>Gred: $gred_count</p>";
    echo "<p>Pengguna: $pengguna_count</p>";
    
    echo "<h3 style='color: green;'>✓ Setup database dengan unit selesai!</h3>";
    
    // Papar maklumat login
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Maklumat Login Lalai:</h4>";
    echo "<p><strong>Pentadbir:</strong><br>";
    echo "No. KP: <code>123456789012</code><br>";
    echo "Kata laluan: <code>password</code><br>";
    echo "Unit: PENGARAH</p>";
    echo "<p><strong>Pengguna:</strong><br>";
    echo "No. KP: <code>345678901234</code><br>";
    echo "Kata laluan: <code>password</code><br>";
    echo "Unit: KESIHATAN AWAM</p>";
    echo "</div>";
    
    // Pautan ke sistem
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='daftar.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Daftar</a>";
    echo "<a href='log_masuk.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk</a>";
    echo "<a href='debug_dropdown.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Debug</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat Setup:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Database Dengan Unit</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f8f9fa; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h3 { margin-top: 30px; }
        p { margin: 10px 0; }
        a { display: inline-block; margin: 5px; }
    </style>
</head>
<body>
</body>
</html>
