<?php
/**
 * Halaman Log Masuk - Sistem Sebenar
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Jika sudah log masuk, redirect ke dashboard
if (isset($_SESSION['pengguna_id'])) {
    if ($_SESSION['profile_id'] == 3) {
        header('Location: senarai_tempahan_penyelaras.php');
    } else {
        header('Location: dashboard_sebenar.php');
    }
    exit;
}

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

$mesej_ralat = '';

// Proses log masuk
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nokp = trim($_POST['nokp']);
    $kata_laluan = $_POST['kata_laluan'];
    
    if (empty($nokp) || empty($kata_laluan)) {
        $mesej_ralat = 'Sila masukkan No. KP dan kata laluan.';
    } else {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "SELECT p.id, p.nokp, p.kata_laluan, p.nama_penuh, p.emel, p.profile_id, p.status,
                           pr.name as profile_name, pr.peranan as profile_peranan
                    FROM pengguna p
                    JOIN profile pr ON p.profile_id = pr.id
                    WHERE p.nokp = ? AND p.status = 'aktif'";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$nokp]);
            $pengguna = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($pengguna && password_verify($kata_laluan, $pengguna['kata_laluan'])) {
                // Set session
                $_SESSION['pengguna_id'] = $pengguna['id'];
                $_SESSION['nokp'] = $pengguna['nokp'];
                $_SESSION['nama_penuh'] = $pengguna['nama_penuh'];
                $_SESSION['emel'] = $pengguna['emel'];
                $_SESSION['profile_id'] = $pengguna['profile_id'];
                $_SESSION['profile_name'] = $pengguna['profile_name'];
                $_SESSION['profile_peranan'] = $pengguna['profile_peranan'];
                
                // Redirect berdasarkan profile
                switch ($pengguna['profile_id']) {
                    case 2: // Admin
                        header('Location: dashboard_admin.php');
                        break;
                    case 3: // Penyelaras Bilik
                        header('Location: senarai_tempahan_penyelaras.php');
                        break;
                    case 1: // Pengguna
                    default: // Default ke pengguna
                        header('Location: dashboard_pengguna.php');
                        break;
                }
                exit;
            } else {
                $mesej_ralat = 'No. KP atau kata laluan tidak betul.';
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat sistem. Sila cuba lagi.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Masuk - Sistem Tempahan Bilik Mesyuarat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card login-card border-0">
                    <div class="card-header login-header text-center py-4">
                        <i class="bi bi-building fs-1 mb-3"></i>
                        <h3 class="mb-0">Sistem Tempahan</h3>
                        <p class="mb-0">Bilik Mesyuarat</p>
                    </div>
                    
                    <div class="card-body p-5">
                        <h4 class="text-center mb-4">Log Masuk</h4>
                        
                        <?php if (!empty($mesej_ralat)): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="nokp" class="form-label">
                                    <i class="bi bi-person-badge me-2"></i>No. Kad Pengenalan
                                </label>
                                <input type="text" class="form-control" id="nokp" name="nokp" 
                                       value="<?= htmlspecialchars($_POST['nokp'] ?? '') ?>" 
                                       placeholder="Contoh: 123456789012" required>
                                <small class="text-muted">Masukkan No. KP tanpa tanda '-'</small>
                            </div>
                            
                            <div class="mb-4">
                                <label for="kata_laluan" class="form-label">
                                    <i class="bi bi-lock me-2"></i>Kata Laluan
                                </label>
                                <input type="password" class="form-control" id="kata_laluan" name="kata_laluan" 
                                       placeholder="Masukkan kata laluan" required>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Log Masuk
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <strong>Akaun Lalai:</strong><br>
                                Admin: <code>123456789012</code> / <code>password</code><br>
                                Penyelaras: <code>234567890123</code> / <code>password</code><br>
                                Pengguna: <code>345678901234</code> / <code>password</code>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Info -->
                <div class="card mt-3 border-0" style="background: rgba(255, 255, 255, 0.9);">
                    <div class="card-body">
                        <h6 class="text-center mb-3">Jenis Pengguna:</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="bi bi-shield-check fs-3 text-danger"></i>
                                <p class="small mb-0"><strong>Admin</strong></p>
                                <p class="small text-muted">Akses penuh sistem</p>
                            </div>
                            <div class="col-4">
                                <i class="bi bi-gear fs-3 text-warning"></i>
                                <p class="small mb-0"><strong>Penyelaras</strong></p>
                                <p class="small text-muted">Kelola tempahan bilik</p>
                            </div>
                            <div class="col-4">
                                <i class="bi bi-person fs-3 text-primary"></i>
                                <p class="small mb-0"><strong>Pengguna</strong></p>
                                <p class="small text-muted">Buat tempahan</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validasi No. KP
        document.getElementById('nokp').addEventListener('input', function() {
            const nokp = this.value.replace(/\D/g, ''); // Buang semua bukan digit
            this.value = nokp;
        });
    </script>
</body>
</html>
