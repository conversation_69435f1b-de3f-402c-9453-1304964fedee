<?php
/**
 * <PERSON><PERSON><PERSON> Si<PERSON>m
 * Sistem Tempahan Bilik Mesyuarat
 * 
 * Fail ini akan menguji fungsi-fungsi asas sistem
 */

require_once 'config/sistem_config.php';

echo "<h2>Ujian Sistem - Sistem Tempahan Bilik Mesyuarat</h2>";

$tests_passed = 0;
$tests_total = 0;

function runTest($test_name, $test_function) {
    global $tests_passed, $tests_total;
    $tests_total++;
    
    echo "<h3>$test_name</h3>";
    
    try {
        $result = $test_function();
        if ($result) {
            echo "<p style='color: green;'>✓ LULUS</p>";
            $tests_passed++;
        } else {
            echo "<p style='color: red;'>✗ GAGAL</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ RALAT: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Test 1: Sambungan Pangkalan Data
runTest("1. Sambungan Pang<PERSON> Data", function() use ($db) {
    $result = $db->fetch("SELECT 1 as test");
    return $result['test'] == 1;
});

// Test 2: Jadual Wujud
runTest("2. Semak Jadual Wujud", function() use ($db) {
    $tables = ['pengguna', 'tbahagian', 'tjawatan', 'tgred', 'bilik_mesyuarat', 'tempahan', 'tetapan_sistem', 'notifikasi'];
    $existing_tables = $db->fetchAll("SHOW TABLES");
    $existing_table_names = array_column($existing_tables, 'Tables_in_sistem_tempahan_bilik');

    foreach ($tables as $table) {
        if (!in_array($table, $existing_table_names)) {
            echo "<p>Jadual '$table' tidak wujud</p>";
            return false;
        }
    }

    echo "<p>Semua jadual wujud: " . implode(', ', $tables) . "</p>";
    return true;
});

// Test 3: Data Awal
runTest("3. Semak Data Awal", function() use ($db) {
    // Semak pengguna
    $user_count = $db->fetch("SELECT COUNT(*) as count FROM pengguna")['count'];
    echo "<p>Pengguna: $user_count</p>";

    // Semak bahagian
    $dept_count = $db->fetch("SELECT COUNT(*) as count FROM tbahagian")['count'];
    echo "<p>Bahagian: $dept_count</p>";

    // Semak jawatan
    $jawatan_count = $db->fetch("SELECT COUNT(*) as count FROM tjawatan")['count'];
    echo "<p>Jawatan: $jawatan_count</p>";

    // Semak gred
    $gred_count = $db->fetch("SELECT COUNT(*) as count FROM tgred")['count'];
    echo "<p>Gred: $gred_count</p>";

    // Semak bilik
    $room_count = $db->fetch("SELECT COUNT(*) as count FROM bilik_mesyuarat")['count'];
    echo "<p>Bilik: $room_count</p>";

    // Semak tetapan
    $setting_count = $db->fetch("SELECT COUNT(*) as count FROM tetapan_sistem")['count'];
    echo "<p>Tetapan: $setting_count</p>";

    return $user_count > 0 && $dept_count > 0 && $jawatan_count > 0 && $gred_count > 0 && $room_count > 0 && $setting_count > 0;
});

// Test 4: Fungsi Konfigurasi
runTest("4. Fungsi Konfigurasi", function() use ($config) {
    if (!$config) {
        echo "<p>Objek konfigurasi tidak wujud</p>";
        return false;
    }
    
    $nama_org = $config->dapatkan('nama_organisasi', 'Default');
    echo "<p>Nama organisasi: $nama_org</p>";
    
    $masa_min = $config->dapatkan('masa_tempahan_minimum', 30);
    echo "<p>Masa minimum: $masa_min minit</p>";
    
    return !empty($nama_org) && $masa_min > 0;
});

// Test 5: Fungsi Helper
runTest("5. Fungsi Helper", function() {
    // Test formatTarikh
    $tarikh = formatTarikh('2024-01-15');
    echo "<p>Format tarikh: $tarikh</p>";
    
    // Test formatMasa
    $masa = formatMasa('14:30:00');
    echo "<p>Format masa: $masa</p>";
    
    // Test formatWang
    $wang = formatWang(150.50);
    echo "<p>Format wang: $wang</p>";
    
    // Test janaKodTempahan
    $kod = janaKodTempahan();
    echo "<p>Kod tempahan: $kod</p>";
    
    return !empty($tarikh) && !empty($masa) && !empty($wang) && !empty($kod);
});

// Test 6: Validasi Input
runTest("6. Validasi Input", function() {
    $input_kotor = "<script>alert('test')</script>Test Input";
    $input_bersih = bersihkanInput($input_kotor);
    echo "<p>Input asal: $input_kotor</p>";
    echo "<p>Input bersih: $input_bersih</p>";
    
    return $input_bersih !== $input_kotor && strpos($input_bersih, '<script>') === false;
});

// Test 7: Sistem Mesej
runTest("7. Sistem Mesej", function() {
    $mesej_kejayaan = paparMesej('kejayaan', 'Test mesej kejayaan');
    $mesej_ralat = paparMesej('ralat', 'Test mesej ralat');
    
    echo "<div>$mesej_kejayaan</div>";
    echo "<div>$mesej_ralat</div>";
    
    return strpos($mesej_kejayaan, 'alert-success') !== false && 
           strpos($mesej_ralat, 'alert-danger') !== false;
});

// Test 8: Query Bilik
runTest("8. Query Bilik Mesyuarat", function() use ($db) {
    $bilik = $db->fetchAll("SELECT * FROM bilik_mesyuarat WHERE status = 'tersedia' LIMIT 3");
    
    echo "<p>Bilik tersedia:</p>";
    echo "<ul>";
    foreach ($bilik as $b) {
        echo "<li>{$b['nama_bilik']} (Kapasiti: {$b['kapasiti']})</li>";
    }
    echo "</ul>";
    
    return count($bilik) > 0;
});

// Test 9: Query Pengguna
runTest("9. Query Pengguna", function() use ($db) {
    $pengguna = $db->fetchAll("SELECT p.nokp, p.nama_penuh, p.peranan, b.bahagian, j.jawatan, g.gred
                               FROM pengguna p
                               LEFT JOIN tbahagian b ON p.bahagian_id = b.id
                               LEFT JOIN tjawatan j ON p.jawatan_id = j.id
                               LEFT JOIN tgred g ON p.gred_id = g.id
                               WHERE p.status = 'aktif' LIMIT 3");

    echo "<p>Pengguna aktif:</p>";
    echo "<ul>";
    foreach ($pengguna as $p) {
        echo "<li>{$p['nama_penuh']} (No. KP: {$p['nokp']}) - {$p['peranan']}<br>";
        echo "Bahagian: {$p['bahagian']}, Jawatan: {$p['jawatan']}, Gred: {$p['gred']}</li>";
    }
    echo "</ul>";

    return count($pengguna) > 0;
});

// Test 10: Fail Sistem Wujud
runTest("10. Fail Sistem Wujud", function() {
    $files = [
        'config/sistem_config.php',
        'includes/header_sistem.php',
        'includes/footer_sistem.php',
        'log_masuk.php',
        'daftar.php',
        'dashboard.php',
        'senarai_bilik.php',
        'tempah_bilik.php',
        'tempahan_saya.php',
        'kalendar.php',
        'halaman_utama.php'
    ];
    
    $missing_files = [];
    foreach ($files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }
    
    if (!empty($missing_files)) {
        echo "<p>Fail yang hilang:</p>";
        echo "<ul>";
        foreach ($missing_files as $file) {
            echo "<li style='color: red;'>$file</li>";
        }
        echo "</ul>";
        return false;
    }
    
    echo "<p>Semua fail sistem wujud (" . count($files) . " fail)</p>";
    return true;
});

// Ringkasan Ujian
echo "<h2>Ringkasan Ujian</h2>";
echo "<p><strong>Ujian Lulus:</strong> $tests_passed / $tests_total</p>";

if ($tests_passed == $tests_total) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✓ Semua Ujian Lulus!</h3>";
    echo "<p>Sistem tempahan bilik mesyuarat siap untuk digunakan.</p>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h4>Sila uji halaman-halaman berikut:</h4>";
    echo "<a href='halaman_utama.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Halaman Utama</a>";
    echo "<a href='log_masuk.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk</a>";
    echo "<a href='daftar.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Daftar</a>";
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠ Beberapa Ujian Gagal</h3>";
    echo "<p>Sila semak ralat di atas dan betulkan sebelum menggunakan sistem.</p>";
    echo "</div>";
}

// Maklumat Sistem
echo "<h3>Maklumat Sistem</h3>";
echo "<table style='width: 100%; border-collapse: collapse; background: white;'>";
echo "<tr style='background: #f8f9fa;'><td style='padding: 8px; border: 1px solid #ddd;'><strong>PHP Version</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>" . PHP_VERSION . "</td></tr>";
echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>MySQL Version</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>";
try {
    $version = $db->fetch("SELECT VERSION() as version")['version'];
    echo $version;
} catch (Exception $e) {
    echo "Tidak dapat dikesan";
}
echo "</td></tr>";
echo "<tr style='background: #f8f9fa;'><td style='padding: 8px; border: 1px solid #ddd;'><strong>Masa Server</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>" . date('Y-m-d H:i:s') . "</td></tr>";
echo "<tr><td style='padding: 8px; border: 1px solid #ddd;'><strong>Zon Waktu</strong></td><td style='padding: 8px; border: 1px solid #ddd;'>" . date_default_timezone_get() . "</td></tr>";
echo "</table>";
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ujian Sistem - Sistem Tempahan Bilik Mesyuarat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #555;
            margin-top: 25px;
        }
        
        p {
            margin: 8px 0;
        }
        
        ul {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        li {
            margin: 5px 0;
        }
        
        hr {
            border: none;
            border-top: 1px solid #ddd;
            margin: 20px 0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        a {
            display: inline-block;
            margin: 5px;
            text-decoration: none;
        }
        
        table {
            margin: 15px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-radius: 5px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- Content generated by PHP above -->
</body>
</html>
