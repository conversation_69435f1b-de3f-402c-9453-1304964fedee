        </div> <!-- End container -->
    </div> <!-- End main-content -->
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="bi bi-building me-2"></i><?= NAMA_SISTEM ?></h5>
                    <p class="mb-2">Sistem pengurusan tempahan bilik mesyuarat yang mudah dan cekap.</p>
                    <p class="mb-0">
                        <small>
                            <i class="bi bi-envelope me-1"></i><?= EMEL_ADMIN ?><br>
                            <i class="bi bi-clock me-1"></i>Waktu Operasi: 8:00 AM - 6:00 PM
                        </small>
                    </p>
                </div>
                
                <div class="col-md-3">
                    <h6>Pautan Pantas</h6>
                    <ul class="list-unstyled">
                        <?php if (semakLogin()): ?>
                            <li><a href="<?= URL_SISTEM ?>/dashboard.php" class="text-light text-decoration-none">
                                <i class="bi bi-house me-1"></i>Dashboard
                            </a></li>
                            <li><a href="<?= URL_SISTEM ?>/senarai_bilik.php" class="text-light text-decoration-none">
                                <i class="bi bi-door-open me-1"></i>Bilik Mesyuarat
                            </a></li>
                            <li><a href="<?= URL_SISTEM ?>/tempah_bilik_sebenar.php" class="text-light text-decoration-none">
                                <i class="bi bi-calendar-plus me-1"></i>Tempah Bilik
                            </a></li>
                            <li><a href="<?= URL_SISTEM ?>/tempahan_saya.php" class="text-light text-decoration-none">
                                <i class="bi bi-calendar-check me-1"></i>Tempahan Saya
                            </a></li>
                        <?php else: ?>
                            <li><a href="<?= URL_SISTEM ?>/log_masuk.php" class="text-light text-decoration-none">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Log Masuk
                            </a></li>
                            <li><a href="<?= URL_SISTEM ?>/daftar.php" class="text-light text-decoration-none">
                                <i class="bi bi-person-plus me-1"></i>Daftar Akaun
                            </a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="col-md-3">
                    <h6>Maklumat Sistem</h6>
                    <ul class="list-unstyled">
                        <li><small><i class="bi bi-info-circle me-1"></i>Versi: <?= VERSI_SISTEM ?></small></li>
                        <li><small><i class="bi bi-calendar me-1"></i>Tahun: <?= date('Y') ?></small></li>
                        <li><small><i class="bi bi-clock me-1"></i>Masa: <?= date('H:i') ?></small></li>
                        <?php if (semakLogin()): ?>
                            <li><small><i class="bi bi-person me-1"></i>Pengguna: <?= htmlspecialchars($_SESSION['nokp']) ?></small></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            
            <hr class="my-3">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small>&copy; <?= date('Y') ?> <?= NAMA_SISTEM ?>. Hak cipta terpelihara.</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small>
                        Dibangunkan dengan <i class="bi bi-heart-fill text-danger"></i> 
                        menggunakan PHP & Bootstrap
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
        
        // Confirm delete actions
        function confirmDelete(message = 'Adakah anda pasti ingin memadam item ini?') {
            return confirm(message);
        }
        
        // Format currency input
        function formatCurrency(input) {
            let value = input.value.replace(/[^\d.]/g, '');
            if (value) {
                input.value = 'RM ' + parseFloat(value).toFixed(2);
            }
        }
        
        // Validate time range
        function validateTimeRange(startTime, endTime) {
            if (startTime && endTime) {
                const start = new Date('2000-01-01 ' + startTime);
                const end = new Date('2000-01-01 ' + endTime);
                
                if (end <= start) {
                    alert('Masa tamat mestilah selepas masa mula!');
                    return false;
                }
            }
            return true;
        }
        
        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ms-MY', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const clockElements = document.querySelectorAll('.live-clock');
            clockElements.forEach(function(element) {
                element.textContent = timeString;
            });
        }
        
        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call
        
        // Tooltip initialization
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Popover initialization
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    </script>
    
    <?php if (isset($custom_js)): ?>
        <!-- Custom JavaScript for specific pages -->
        <script><?= $custom_js ?></script>
    <?php endif; ?>
    
</body>
</html>
