# Sistem Tempahan Bilik Mesyuarat

## Pengenalan

Sistem Tempahan Bilik Mesyuarat adalah aplikasi web yang dibangunkan sepenuhnya dalam **Bahasa Melayu** untuk menguruskan tempahan bilik mesyuarat secara digital. Sistem ini menyediakan platform yang mudah, cekap dan mesra pengguna untuk organisasi menguruskan kemudahan bilik mesyuarat mereka.

## Ciri-ciri Utama

### 🏢 Pengurusan Bilik
- Senarai bilik mesyuarat dengan maklumat lengkap
- Paparan grid dan senarai untuk kemudahan navigasi
- Maklumat kapasiti, lokasi, kem<PERSON>han dan kadar sewa
- Status ketersediaan real-time
- Gambar dan penerangan bilik

### 📅 Sistem Tempahan
- Borang tempahan yang mudah digunakan
- Validasi ketersediaan masa secara automatik
- Pengiraan kos tempahan secara automatik
- Sistem kelulusan bertingkat
- Notifikasi automatik untuk pengguna

### 👥 Pengurusan Pengguna
- Sistem pendaftaran dan log masuk yang selamat
- Tiga tahap pengguna: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Pengguna
- <PERSON>il pengguna yang boleh dikemaskini
- Pengurusan kata laluan yang selamat

### 📊 Dashboard dan Laporan
- Dashboard interaktif dengan statistik real-time
- Kalendar tempahan visual
- Laporan penggunaan bilik
- Statistik tempahan dan analitik

### 🔔 Sistem Notifikasi
- Notifikasi dalam sistem
- Pemberitahuan status tempahan
- Peringatan mesyuarat akan datang
- Log aktiviti pengguna

## Struktur Fail Sistem

```
sistem_tempahan_bilik/
├── config/
│   ├── database.php          # Konfigurasi pangkalan data
│   ├── sistem_config.php     # Konfigurasi sistem utama
│   └── config.php            # Konfigurasi asal (untuk rujukan)
├── database/
│   └── sistem_tempahan_bilik.sql  # Skrip pangkalan data
├── includes/
│   ├── header_sistem.php     # Header sistem Bahasa Melayu
│   ├── footer_sistem.php     # Footer sistem
│   ├── header.php            # Header asal (untuk rujukan)
│   └── footer.php            # Footer asal (untuk rujukan)
├── admin/                    # Panel pentadbiran (akan dibangunkan)
├── classes/                  # Kelas PHP (sistem asal)
├── ajax/                     # Fail AJAX (sistem asal)
├── halaman_utama.php         # Halaman utama sistem Bahasa Melayu
├── log_masuk.php            # Halaman log masuk
├── daftar.php               # Halaman pendaftaran
├── log_keluar.php           # Proses log keluar
├── dashboard.php            # Dashboard pengguna
├── senarai_bilik.php        # Senarai bilik mesyuarat
├── tempah_bilik.php         # Borang tempahan bilik
├── tempahan_saya.php        # Senarai tempahan pengguna (akan dibangunkan)
├── kalendar.php             # Kalendar tempahan (akan dibangunkan)
└── README_SISTEM_MELAYU.md  # Dokumentasi ini
```

## Keperluan Sistem

### Pelayan Web
- **PHP**: 7.4 atau lebih tinggi
- **MySQL**: 5.7 atau lebih tinggi / MariaDB 10.2+
- **Apache/Nginx**: Dengan mod_rewrite diaktifkan
- **XAMPP/WAMP/LAMP**: Untuk pembangunan tempatan

### Perpustakaan PHP
- PDO MySQL
- Session support
- JSON support
- Password hashing (PHP 5.5+)

### Frontend
- **Bootstrap 5.3.0**: Framework CSS
- **Bootstrap Icons**: Set ikon
- **JavaScript**: ES6+ support

## Pemasangan

### 1. Persediaan Pangkalan Data

```sql
-- Cipta pangkalan data
CREATE DATABASE sistem_tempahan_bilik CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import skrip pangkalan data
mysql -u root -p sistem_tempahan_bilik < database/sistem_tempahan_bilik.sql
```

### 2. Konfigurasi Sistem

Kemaskini fail `config/database.php`:

```php
private $host = 'localhost';
private $db_name = 'sistem_tempahan_bilik';
private $username = 'root';
private $password = 'kata_laluan_anda';
```

### 3. Tetapan Pelayan Web

Pastikan direktori sistem boleh diakses melalui pelayan web:
- **XAMPP**: Letakkan dalam `htdocs/sistem_tempahan`
- **WAMP**: Letakkan dalam `www/sistem_tempahan`
- **LAMP**: Letakkan dalam `/var/www/html/sistem_tempahan`

### 4. Ujian Sistem

1. Akses halaman utama: `http://localhost/sistem_tempahan/halaman_utama.php`
2. Log masuk dengan akaun lalai:
   - **Pentadbir**: `pentadbir` / `password`
   - **Pengguna**: `pengguna1` / `password`

## Penggunaan Sistem

### Untuk Pengguna Biasa

1. **Daftar Akaun**
   - Akses `daftar.php`
   - Isi maklumat peribadi
   - Tunggu aktivasi akaun

2. **Log Masuk**
   - Gunakan nama pengguna dan kata laluan
   - Akses dashboard peribadi

3. **Tempah Bilik**
   - Lihat senarai bilik tersedia
   - Pilih tarikh dan masa
   - Isi borang tempahan
   - Tunggu kelulusan

4. **Uruskan Tempahan**
   - Lihat tempahan sedia ada
   - Kemaskini atau batalkan tempahan
   - Terima notifikasi status

### Untuk Pentadbir

1. **Pengurusan Bilik**
   - Tambah/kemaskini bilik baru
   - Tetapkan kapasiti dan kemudahan
   - Uruskan status ketersediaan

2. **Pengurusan Pengguna**
   - Luluskan pendaftaran baru
   - Uruskan peranan pengguna
   - Nyahaktifkan akaun

3. **Pengurusan Tempahan**
   - Luluskan/tolak tempahan
   - Lihat kalendar keseluruhan
   - Jana laporan penggunaan

## Struktur Pangkalan Data

### Jadual Utama

1. **pengguna** - Maklumat pengguna sistem
2. **bahagian** - Bahagian organisasi
3. **bilik_mesyuarat** - Maklumat bilik mesyuarat
4. **tempahan** - Rekod tempahan bilik
5. **sejarah_tempahan** - Audit trail tempahan
6. **tetapan_sistem** - Konfigurasi sistem
7. **notifikasi** - Sistem notifikasi

### Hubungan Jadual

```
pengguna (1) ←→ (n) tempahan
bilik_mesyuarat (1) ←→ (n) tempahan
bahagian (1) ←→ (n) bilik_mesyuarat
pengguna (1) ←→ (n) notifikasi
tempahan (1) ←→ (n) sejarah_tempahan
```

## Ciri Keselamatan

### Autentikasi
- Password hashing menggunakan PHP `password_hash()`
- Session management yang selamat
- CSRF token protection
- Input validation dan sanitization

### Kawalan Akses
- Role-based access control (RBAC)
- Pemeriksaan kebenaran pada setiap halaman
- Perlindungan terhadap SQL injection
- XSS protection

### Audit Trail
- Log semua aktiviti pengguna
- Sejarah perubahan tempahan
- Tracking login/logout
- Monitoring akses sistem

## Pembangunan Lanjutan

### Ciri Yang Akan Ditambah

1. **Kalendar Interaktif**
   - Paparan kalendar bulanan
   - Drag & drop tempahan
   - Integrasi dengan kalendar luaran

2. **Sistem Laporan**
   - Laporan penggunaan bilik
   - Statistik tempahan
   - Export ke PDF/Excel

3. **Panel Pentadbiran**
   - Dashboard pentadbir lengkap
   - Pengurusan sistem menyeluruh
   - Konfigurasi lanjutan

4. **API Integration**
   - REST API untuk integrasi
   - Mobile app support
   - Third-party integrations

### Struktur Kod

```php
// Contoh penggunaan konfigurasi sistem
require_once 'config/sistem_config.php';

// Semak login
perluLogin();

// Dapatkan data
$data = $db->fetchAll("SELECT * FROM bilik_mesyuarat WHERE status = ?", ['tersedia']);

// Papar mesej
echo paparMesej('kejayaan', 'Tempahan berjaya dibuat!');
```

## Sokongan dan Bantuan

### Dokumentasi
- Kod disertakan dengan komen lengkap dalam Bahasa Melayu
- Fungsi helper untuk kemudahan pembangunan
- Contoh penggunaan untuk setiap modul

### Penyelesaian Masalah

**Masalah Biasa:**

1. **Ralat sambungan pangkalan data**
   - Semak konfigurasi dalam `config/database.php`
   - Pastikan MySQL berjalan
   - Periksa nama pangkalan data dan kredensial

2. **Halaman kosong/ralat PHP**
   - Aktifkan error reporting untuk pembangunan
   - Semak log ralat pelayan web
   - Pastikan semua fail diperlukan wujud

3. **Masalah session**
   - Pastikan session_start() dipanggil
   - Semak konfigurasi session PHP
   - Periksa kebenaran direktori session

## Lesen dan Hak Cipta

Sistem ini dibangunkan untuk kegunaan organisasi dan boleh disesuaikan mengikut keperluan. 

**Teknologi yang digunakan:**
- PHP 8.0+
- MySQL 8.0+
- Bootstrap 5.3.0
- Bootstrap Icons 1.10.0

---

*Sistem Tempahan Bilik Mesyuarat v1.0 - Dibangunkan dengan ❤️ dalam Bahasa Melayu*
