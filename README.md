# Meeting Room Booking System

A comprehensive PHP-based meeting room booking system with MySQL database, designed for efficient management of meeting room reservations in corporate environments.

## Features

### User Features
- **User Registration & Authentication**: Secure user registration and login system
- **Room Browsing**: View available meeting rooms with detailed information
- **Advanced Search**: Filter rooms by capacity, location, and facilities
- **Booking Management**: Create, view, edit, and cancel bookings
- **Calendar View**: Visual calendar interface to view bookings
- **Real-time Availability**: Check room availability in real-time
- **Cost Calculation**: Automatic calculation of booking costs

### Admin Features
- **Admin Dashboard**: Comprehensive overview of system statistics
- **Room Management**: Add, edit, and manage meeting rooms
- **Booking Management**: View and manage all bookings
- **User Management**: Manage user accounts and permissions
- **Booking Approval**: Confirm or reject pending bookings

### Technical Features
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5
- **Security**: CSRF protection, input sanitization, and secure authentication
- **Database**: Well-structured MySQL database with proper relationships
- **AJAX Integration**: Dynamic content loading for better user experience
- **Session Management**: Secure session handling with timeout

## System Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

## Installation

### 1. Database Setup
```sql
-- Import the database schema
mysql -u username -p < database/schema.sql
```

### 2. Configuration
Edit `config/database.php` to match your database settings:
```php
private $host = 'localhost';
private $db_name = 'meeting_room_booking';
private $username = 'your_username';
private $password = 'your_password';
```

### 3. File Permissions
Ensure proper file permissions for the web server to read/write files.

### 4. Web Server Configuration
Point your web server document root to the project directory.

## Default Accounts

The system comes with pre-configured demo accounts:

### Admin Account
- **Username**: admin
- **Password**: password
- **Email**: <EMAIL>

### User Accounts
- **Username**: john_doe
- **Password**: password
- **Email**: <EMAIL>

- **Username**: jane_smith
- **Password**: password
- **Email**: <EMAIL>

## File Structure

```
jkndata/
├── admin/                  # Admin panel files
│   ├── dashboard.php      # Admin dashboard
│   └── rooms.php          # Room management
├── ajax/                  # AJAX endpoints
│   ├── confirm_booking.php
│   └── get_room_bookings.php
├── classes/               # PHP classes
│   ├── User.php          # User management
│   ├── Room.php          # Room management
│   └── Booking.php       # Booking management
├── config/                # Configuration files
│   ├── config.php        # Main configuration
│   └── database.php      # Database connection
├── database/              # Database files
│   └── schema.sql        # Database schema
├── includes/              # Common includes
│   ├── header.php        # Page header
│   └── footer.php        # Page footer
├── index.php             # Dashboard
├── login.php             # Login page
├── register.php          # Registration page
├── rooms.php             # Room listing
├── book_room.php         # Room booking
├── my_bookings.php       # User bookings
├── calendar.php          # Calendar view
└── README.md             # This file
```

## Database Schema

### Tables
- **users**: User accounts and authentication
- **meeting_rooms**: Meeting room information
- **bookings**: Booking records
- **booking_history**: Audit trail for bookings

### Key Relationships
- Users can have multiple bookings
- Rooms can have multiple bookings
- Bookings belong to one user and one room
- Booking history tracks all changes

## Usage

### For Users
1. Register for an account or login
2. Browse available meeting rooms
3. Select a room and choose date/time
4. Fill in booking details and submit
5. View and manage your bookings
6. Use calendar view to see all bookings

### For Administrators
1. Login with admin credentials
2. Access admin panel from navigation
3. Manage rooms, bookings, and users
4. Review and approve pending bookings
5. Monitor system statistics

## Security Features

- **Password Hashing**: Secure password storage using PHP's password_hash()
- **CSRF Protection**: Cross-site request forgery protection
- **Input Sanitization**: All user inputs are sanitized
- **Session Security**: Secure session management with timeout
- **SQL Injection Prevention**: Prepared statements for all database queries
- **Access Control**: Role-based access control for admin functions

## Customization

### Business Rules
Edit `config/config.php` to modify:
- Business hours
- Booking duration limits
- Advance booking period
- Time zone settings

### Styling
The system uses Bootstrap 5 with custom CSS. Modify the styles in `includes/header.php` to customize the appearance.

### Features
Add new features by:
1. Creating new PHP classes in the `classes/` directory
2. Adding new pages following the existing structure
3. Updating the navigation in `includes/header.php`

## Troubleshooting

### Common Issues
1. **Database Connection Error**: Check database credentials in `config/database.php`
2. **Permission Denied**: Ensure proper file permissions
3. **Session Issues**: Check PHP session configuration
4. **AJAX Errors**: Verify file paths and permissions

### Debug Mode
Enable debug mode by setting the environment in `config/config.php`:
```php
$_ENV['APP_ENV'] = 'development';
```

## Support

For support and questions:
- Email: <EMAIL>
- Phone: +60 3-1234 5678
- Business Hours: Mon-Fri, 8:00 AM - 6:00 PM

## License

This project is proprietary software. All rights reserved.

## Version History

- **v1.0.0** - Initial release with core functionality
  - User authentication and registration
  - Room management and booking
  - Admin panel and calendar view
  - Responsive design and security features
