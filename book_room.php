<?php
require_once 'config/config.php';
require_once 'classes/Room.php';
require_once 'classes/Booking.php';

requireLogin();

$room = new Room();
$booking = new Booking();

$room_id = (int)($_GET['room_id'] ?? 0);
$room_data = $room->getRoomById($room_id);

if (!$room_data) {
    redirect('rooms.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token';
    } else {
        $booking_date = $_POST['booking_date'] ?? '';
        $start_time = $_POST['start_time'] ?? '';
        $end_time = $_POST['end_time'] ?? '';
        $purpose = sanitizeInput($_POST['purpose'] ?? '');
        $attendees = (int)($_POST['attendees'] ?? 1);
        $notes = sanitizeInput($_POST['notes'] ?? '');

        // Validation
        if (empty($booking_date) || empty($start_time) || empty($end_time) || empty($purpose)) {
            $error = 'Please fill in all required fields';
        } elseif (strtotime($booking_date) < strtotime(date('Y-m-d'))) {
            $error = 'Cannot book for past dates';
        } elseif (strtotime($start_time) >= strtotime($end_time)) {
            $error = 'End time must be after start time';
        } elseif ($attendees > $room_data['capacity']) {
            $error = 'Number of attendees exceeds room capacity';
        } else {
            $booking_data = [
                'user_id' => $_SESSION['user_id'],
                'room_id' => $room_id,
                'booking_date' => $booking_date,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'purpose' => $purpose,
                'attendees' => $attendees,
                'notes' => $notes
            ];

            $result = $booking->createBooking($booking_data);
            if ($result['success']) {
                redirect('my_bookings.php?success=' . urlencode($result['message']));
            } else {
                $error = $result['message'];
            }
        }
    }
}

$page_title = 'Book Room - ' . $room_data['room_name'];
include 'includes/header.php';
?>

<div class="container">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-calendar-plus"></i> Book Meeting Room</h4>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="bookingForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="booking_date" class="form-label">Date *</label>
                                <input type="date" class="form-control" id="booking_date" name="booking_date" 
                                       min="<?php echo date('Y-m-d'); ?>" 
                                       max="<?php echo date('Y-m-d', strtotime('+' . ADVANCE_BOOKING_DAYS . ' days')); ?>"
                                       value="<?php echo htmlspecialchars($_POST['booking_date'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="start_time" class="form-label">Start Time *</label>
                                <input type="time" class="form-control" id="start_time" name="start_time" 
                                       value="<?php echo htmlspecialchars($_POST['start_time'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="end_time" class="form-label">End Time *</label>
                                <input type="time" class="form-control" id="end_time" name="end_time" 
                                       value="<?php echo htmlspecialchars($_POST['end_time'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="purpose" class="form-label">Purpose/Meeting Title *</label>
                                <input type="text" class="form-control" id="purpose" name="purpose" 
                                       value="<?php echo htmlspecialchars($_POST['purpose'] ?? ''); ?>" 
                                       placeholder="e.g., Weekly Team Meeting" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="attendees" class="form-label">Number of Attendees *</label>
                                <input type="number" class="form-control" id="attendees" name="attendees" 
                                       min="1" max="<?php echo $room_data['capacity']; ?>"
                                       value="<?php echo htmlspecialchars($_POST['attendees'] ?? '1'); ?>" required>
                                <small class="text-muted">Max: <?php echo $room_data['capacity']; ?></small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Any special requirements or notes"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-calculator"></i> Cost Calculation</h6>
                            <p class="mb-1">Hourly Rate: <?php echo formatCurrency($room_data['hourly_rate']); ?></p>
                            <p class="mb-0">Total Cost: <span id="totalCost"><?php echo formatCurrency(0); ?></span></p>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="rooms.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Rooms
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calendar-check"></i> Book Room
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-door-open"></i> Room Details</h5>
                </div>
                <div class="card-body">
                    <?php if ($room_data['image_url']): ?>
                        <img src="<?php echo htmlspecialchars($room_data['image_url']); ?>" 
                             class="img-fluid rounded mb-3" alt="Room Image">
                    <?php endif; ?>
                    
                    <h5><?php echo htmlspecialchars($room_data['room_name']); ?></h5>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($room_data['location']); ?>
                    </p>
                    <p><?php echo htmlspecialchars($room_data['description']); ?></p>
                    
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <strong><i class="fas fa-users"></i> <?php echo $room_data['capacity']; ?></strong><br>
                            <small class="text-muted">Capacity</small>
                        </div>
                        <div class="col-6">
                            <strong><?php echo formatCurrency($room_data['hourly_rate']); ?></strong><br>
                            <small class="text-muted">Per Hour</small>
                        </div>
                    </div>
                    
                    <?php 
                    $facilities = json_decode($room_data['facilities'], true) ?: [];
                    if (!empty($facilities)): 
                    ?>
                        <h6>Facilities:</h6>
                        <ul class="list-unstyled">
                            <?php foreach ($facilities as $facility): ?>
                                <li><i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($facility); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-clock"></i> Today's Bookings</h6>
                </div>
                <div class="card-body">
                    <div id="todayBookings">
                        <p class="text-muted">Select a date to view bookings</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const hourlyRate = <?php echo $room_data['hourly_rate']; ?>;
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    const totalCostSpan = document.getElementById('totalCost');
    const dateInput = document.getElementById('booking_date');

    function calculateCost() {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        
        if (startTime && endTime) {
            const start = new Date('2000-01-01 ' + startTime);
            const end = new Date('2000-01-01 ' + endTime);
            const diffHours = (end - start) / (1000 * 60 * 60);
            
            if (diffHours > 0) {
                const totalCost = diffHours * hourlyRate;
                totalCostSpan.textContent = 'RM ' + totalCost.toFixed(2);
            } else {
                totalCostSpan.textContent = 'RM 0.00';
            }
        }
    }

    function loadTodayBookings() {
        const selectedDate = dateInput.value;
        if (selectedDate) {
            fetch(`ajax/get_room_bookings.php?room_id=<?php echo $room_id; ?>&date=${selectedDate}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('todayBookings');
                    if (data.length === 0) {
                        container.innerHTML = '<p class="text-muted">No bookings for this date</p>';
                    } else {
                        let html = '';
                        data.forEach(booking => {
                            html += `<div class="mb-2 p-2 bg-light rounded">
                                <strong>${booking.start_time} - ${booking.end_time}</strong><br>
                                <small>${booking.purpose}</small>
                            </div>`;
                        });
                        container.innerHTML = html;
                    }
                })
                .catch(error => {
                    console.error('Error loading bookings:', error);
                });
        }
    }

    startTimeInput.addEventListener('change', calculateCost);
    endTimeInput.addEventListener('change', calculateCost);
    dateInput.addEventListener('change', loadTodayBookings);
});
</script>

<?php include 'includes/footer.php'; ?>
