<?php
/**
 * Halaman Kelulusan Tempahan untuk Penyelaras
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kelulusan Tempahan';
require_once 'config/sistem_config.php';

// Semak login dan peranan
perluLogin();

$mesej_ralat = '';
$mesej_kejayaan = '';

// Proses kelulusan/penolakan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['tindakan'])) {
    $tempahan_id = (int)$_POST['tempahan_id'];
    $tindakan = $_POST['tindakan']; // 'lulus' atau 'tolak'
    $catatan = bersihkanInput($_POST['catatan'] ?? '');
    
    try {
        // Semak jika pengguna adalah penyelaras untuk bilik ini
        $sql_check = "SELECT t.*, b.nama_bilik_mesyuarat, bp.idpenyelaras
                      FROM tempahan t
                      JOIN tbilik_mesyuarat b ON t.bilik_id = b.id
                      JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                      WHERE t.id = ? AND bp.idpenyelaras = ? AND t.status = 'menunggu'";
        
        $tempahan = $db->fetch($sql_check, [$tempahan_id, $_SESSION['pengguna_id']]);
        
        if (!$tempahan) {
            $mesej_ralat = 'Anda tidak mempunyai kebenaran untuk meluluskan tempahan ini.';
        } else {
            $status_baru = ($tindakan == 'lulus') ? 'diluluskan' : 'ditolak';
            
            // Kemaskini status tempahan
            $sql_update = "UPDATE tempahan SET 
                          status = ?, 
                          catatan_pentadbir = ?, 
                          diluluskan_oleh = ?, 
                          tarikh_kelulusan = NOW()
                          WHERE id = ?";
            
            $result = $db->query($sql_update, [$status_baru, $catatan, $_SESSION['pengguna_id'], $tempahan_id]);
            
            if ($result) {
                // Hantar notifikasi kepada pemohon
                $mesej_notifikasi = ($tindakan == 'lulus') 
                    ? "Tempahan anda untuk {$tempahan['nama_bilik_mesyuarat']} telah diluluskan."
                    : "Tempahan anda untuk {$tempahan['nama_bilik_mesyuarat']} telah ditolak.";
                
                if (!empty($catatan)) {
                    $mesej_notifikasi .= " Catatan: $catatan";
                }
                
                hantarNotifikasi(
                    $tempahan['pengguna_id'],
                    'Status Tempahan Dikemaskini',
                    $mesej_notifikasi,
                    ($tindakan == 'lulus') ? 'kejayaan' : 'amaran',
                    $tempahan_id
                );
                
                // Log aktiviti
                logAktiviti($_SESSION['pengguna_id'], 'Kelulusan Tempahan', 
                           "Tempahan {$tempahan['kod_tempahan']} $status_baru");
                
                $mesej_kejayaan = "Tempahan berjaya " . (($tindakan == 'lulus') ? 'diluluskan' : 'ditolak') . ".";
            } else {
                $mesej_ralat = 'Ralat semasa mengemaskini status tempahan.';
            }
        }
    } catch (Exception $e) {
        $mesej_ralat = 'Ralat sistem: ' . $e->getMessage();
    }
}

// Dapatkan senarai tempahan yang perlu kelulusan
try {
    $sql = "SELECT t.*, p.nama_penuh as nama_pemohon, p.emel as emel_pemohon,
                   b.nama_bilik_mesyuarat, b.kapasiti, bh.bahagian as nama_bahagian
            FROM tempahan t
            JOIN pengguna p ON t.pengguna_id = p.id
            JOIN tbilik_mesyuarat b ON t.bilik_id = b.id
            JOIN tbahagian bh ON b.bahagian = bh.id
            JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
            WHERE bp.idpenyelaras = ? AND t.status = 'menunggu'
            ORDER BY t.tarikh_tempahan_dibuat DESC";
    
    $tempahan_menunggu = $db->fetchAll($sql, [$_SESSION['pengguna_id']]);
    
    // Dapatkan statistik
    $sql_stats = "SELECT 
                    COUNT(*) as jumlah_menunggu,
                    COUNT(CASE WHEN t.tarikh_tempahan = CURDATE() THEN 1 END) as hari_ini,
                    COUNT(CASE WHEN t.tarikh_tempahan BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as minggu_ini
                  FROM tempahan t
                  JOIN tbilik_mesyuarat b ON t.bilik_id = b.id
                  JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                  WHERE bp.idpenyelaras = ? AND t.status = 'menunggu'";
    
    $statistik = $db->fetch($sql_stats, [$_SESSION['pengguna_id']]);
    
} catch (Exception $e) {
    $tempahan_menunggu = [];
    $statistik = ['jumlah_menunggu' => 0, 'hari_ini' => 0, 'minggu_ini' => 0];
    $mesej_ralat = 'Ralat memuatkan data tempahan: ' . $e->getMessage();
}

require_once 'includes/header_sistem.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Kelulusan Tempahan</h1>
                <p class="text-muted mb-0">Kelola tempahan yang memerlukan kelulusan anda</p>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($mesej_ralat)): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
    </div>
<?php endif; ?>

<?php if (!empty($mesej_kejayaan)): ?>
    <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
    </div>
<?php endif; ?>

<!-- Statistik -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-clock-history fs-1 text-warning mb-2"></i>
                <h3 class="mb-0"><?= $statistik['jumlah_menunggu'] ?></h3>
                <p class="text-muted mb-0">Menunggu Kelulusan</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-calendar-day fs-1 text-info mb-2"></i>
                <h3 class="mb-0"><?= $statistik['hari_ini'] ?></h3>
                <p class="text-muted mb-0">Tempahan Hari Ini</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-calendar-week fs-1 text-primary mb-2"></i>
                <h3 class="mb-0"><?= $statistik['minggu_ini'] ?></h3>
                <p class="text-muted mb-0">Minggu Ini</p>
            </div>
        </div>
    </div>
</div>

<!-- Senarai Tempahan -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>Tempahan Menunggu Kelulusan</h5>
    </div>
    <div class="card-body">
        <?php if (empty($tempahan_menunggu)): ?>
            <div class="text-center py-5">
                <i class="bi bi-check-circle fs-1 text-success mb-3"></i>
                <h4 class="text-muted">Tiada tempahan menunggu kelulusan</h4>
                <p class="text-muted">Semua tempahan telah diproses.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table sistem-table">
                    <thead>
                        <tr>
                            <th>Kod Tempahan</th>
                            <th>Pemohon</th>
                            <th>Bilik</th>
                            <th>Tarikh & Masa</th>
                            <th>Tujuan</th>
                            <th>Peserta</th>
                            <th>Tindakan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tempahan_menunggu as $tempahan): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($tempahan['kod_tempahan']) ?></strong><br>
                                    <small class="text-muted">
                                        <?= formatTarikhMasa($tempahan['tarikh_tempahan_dibuat']) ?>
                                    </small>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($tempahan['nama_pemohon']) ?></strong><br>
                                    <small class="text-muted"><?= htmlspecialchars($tempahan['emel_pemohon']) ?></small>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($tempahan['nama_bilik_mesyuarat']) ?></strong><br>
                                    <small class="text-muted">
                                        <?= htmlspecialchars($tempahan['nama_bahagian']) ?> 
                                        (<?= $tempahan['kapasiti'] ?> orang)
                                    </small>
                                </td>
                                <td>
                                    <strong><?= formatTarikh($tempahan['tarikh_tempahan']) ?></strong><br>
                                    <small class="text-muted">
                                        <?= formatMasa($tempahan['masa_mula']) ?> - 
                                        <?= formatMasa($tempahan['masa_tamat']) ?>
                                    </small>
                                </td>
                                <td>
                                    <?= htmlspecialchars($tempahan['tujuan']) ?>
                                    <?php if (!empty($tempahan['agenda'])): ?>
                                        <br><small class="text-muted">
                                            Agenda: <?= htmlspecialchars(substr($tempahan['agenda'], 0, 50)) ?>...
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info"><?= $tempahan['bilangan_peserta'] ?> orang</span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-success btn-sm me-1" 
                                            onclick="prosesKelulusan(<?= $tempahan['id'] ?>, 'lulus', '<?= htmlspecialchars($tempahan['kod_tempahan']) ?>')">
                                        <i class="bi bi-check-lg"></i> Lulus
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" 
                                            onclick="prosesKelulusan(<?= $tempahan['id'] ?>, 'tolak', '<?= htmlspecialchars($tempahan['kod_tempahan']) ?>')">
                                        <i class="bi bi-x-lg"></i> Tolak
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal Kelulusan -->
<div class="modal fade" id="modalKelulusan" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Kelulusan Tempahan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="tempahan_id" id="tempahan_id">
                    <input type="hidden" name="tindakan" id="tindakan">
                    
                    <div class="mb-3">
                        <label for="catatan" class="form-label">Catatan (Pilihan)</label>
                        <textarea class="form-control" id="catatan" name="catatan" rows="3" 
                                  placeholder="Masukkan catatan jika perlu..."></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <span id="pesanTindakan"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn" id="btnSubmit">Sahkan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php 
$custom_js = "
function prosesKelulusan(tempahan_id, tindakan, kod_tempahan) {
    document.getElementById('tempahan_id').value = tempahan_id;
    document.getElementById('tindakan').value = tindakan;
    
    const modal = document.getElementById('modalKelulusan');
    const title = document.getElementById('modalTitle');
    const pesan = document.getElementById('pesanTindakan');
    const btnSubmit = document.getElementById('btnSubmit');
    
    if (tindakan === 'lulus') {
        title.textContent = 'Luluskan Tempahan';
        pesan.textContent = 'Anda akan meluluskan tempahan ' + kod_tempahan + '. Pemohon akan menerima notifikasi kelulusan.';
        btnSubmit.textContent = 'Luluskan';
        btnSubmit.className = 'btn btn-success';
    } else {
        title.textContent = 'Tolak Tempahan';
        pesan.textContent = 'Anda akan menolak tempahan ' + kod_tempahan + '. Sila berikan sebab penolakan dalam catatan.';
        btnSubmit.textContent = 'Tolak';
        btnSubmit.className = 'btn btn-danger';
    }
    
    new bootstrap.Modal(modal).show();
}
";

require_once 'includes/footer_sistem.php'; 
?>
