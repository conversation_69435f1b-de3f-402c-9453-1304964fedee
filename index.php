<?php
require_once 'config/config.php';
require_once 'classes/Room.php';
require_once 'classes/Booking.php';

requireLogin();

$room = new Room();
$booking = new Booking();

// Get dashboard data
$rooms = $room->getAllRooms();
$user_bookings = $booking->getUserBookings($_SESSION['user_id'], null, 5);
$booking_stats = $booking->getBookingStats();

// Get today's bookings for the user
$today_bookings = $booking->getUserBookings($_SESSION['user_id'], null, null);
$today_bookings = array_filter($today_bookings, function($b) {
    return $b['booking_date'] == date('Y-m-d');
});

$page_title = 'Dashboard';
include 'includes/header.php';
?>

<div class="container">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2><i class="fas fa-home"></i> Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</h2>
                            <p class="mb-0">Manage your meeting room bookings efficiently</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="h4 mb-0"><?php echo formatDate(date('Y-m-d')); ?></div>
                            <div><?php echo date('l'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-door-open fa-2x text-primary mb-2"></i>
                    <h4><?php echo count($rooms); ?></h4>
                    <p class="text-muted mb-0">Available Rooms</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                    <h4><?php echo count($user_bookings); ?></h4>
                    <p class="text-muted mb-0">My Bookings</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-day fa-2x text-warning mb-2"></i>
                    <h4><?php echo count($today_bookings); ?></h4>
                    <p class="text-muted mb-0">Today's Bookings</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-info mb-2"></i>
                    <h4><?php echo date('H:i'); ?></h4>
                    <p class="text-muted mb-0">Current Time</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="rooms.php" class="btn btn-primary">
                            <i class="fas fa-search"></i> Browse Rooms
                        </a>
                        <a href="my_bookings.php" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt"></i> View My Bookings
                        </a>
                        <?php if (isAdmin()): ?>
                            <a href="admin/dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Today's Schedule -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-day"></i> Today's Schedule</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($today_bookings)): ?>
                        <p class="text-muted text-center">No bookings for today</p>
                    <?php else: ?>
                        <?php foreach ($today_bookings as $booking_item): ?>
                            <div class="mb-3 p-2 border rounded">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($booking_item['purpose']); ?></h6>
                                        <small class="text-muted">
                                            <i class="fas fa-door-open"></i> <?php echo htmlspecialchars($booking_item['room_name']); ?><br>
                                            <i class="fas fa-clock"></i> <?php echo formatTime($booking_item['start_time']) . ' - ' . formatTime($booking_item['end_time']); ?>
                                        </small>
                                    </div>
                                    <span class="badge bg-<?php 
                                        echo $booking_item['status'] == 'confirmed' ? 'success' : 
                                            ($booking_item['status'] == 'pending' ? 'warning' : 'secondary'); 
                                    ?>">
                                        <?php echo ucfirst($booking_item['status']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Recent Bookings</h5>
                    <a href="my_bookings.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($user_bookings)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5>No bookings yet</h5>
                            <p class="text-muted">Start by booking a meeting room</p>
                            <a href="rooms.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Book a Room
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Room</th>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Purpose</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($user_bookings as $booking_item): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($booking_item['room_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($booking_item['location']); ?></small>
                                            </td>
                                            <td><?php echo formatDate($booking_item['booking_date']); ?></td>
                                            <td>
                                                <?php echo formatTime($booking_item['start_time']); ?> - 
                                                <?php echo formatTime($booking_item['end_time']); ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($booking_item['purpose']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $booking_item['status'] == 'confirmed' ? 'success' : 
                                                        ($booking_item['status'] == 'pending' ? 'warning' : 
                                                        ($booking_item['status'] == 'cancelled' ? 'danger' : 'info')); 
                                                ?>">
                                                    <?php echo ucfirst($booking_item['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="booking_details.php?id=<?php echo $booking_item['id']; ?>" 
                                                       class="btn btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($booking_item['status'] == 'pending' || $booking_item['status'] == 'confirmed'): ?>
                                                        <?php if (strtotime($booking_item['booking_date'] . ' ' . $booking_item['start_time']) > time()): ?>
                                                            <a href="edit_booking.php?id=<?php echo $booking_item['id']; ?>" 
                                                               class="btn btn-outline-warning" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Rooms -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-star"></i> Popular Meeting Rooms</h5>
                    <a href="rooms.php" class="btn btn-sm btn-outline-primary">View All Rooms</a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach (array_slice($rooms, 0, 3) as $room_data): ?>
                            <div class="col-md-4 mb-3">
                                <div class="card room-card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo htmlspecialchars($room_data['room_name']); ?></h6>
                                        <p class="card-text text-muted small">
                                            <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($room_data['location']); ?>
                                        </p>
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <small class="text-muted">Capacity</small><br>
                                                <strong><i class="fas fa-users"></i> <?php echo $room_data['capacity']; ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Rate/Hour</small><br>
                                                <strong><?php echo formatCurrency($room_data['hourly_rate']); ?></strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <a href="book_room.php?room_id=<?php echo $room_data['id']; ?>" 
                                           class="btn btn-primary btn-sm w-100">
                                            <i class="fas fa-calendar-plus"></i> Book Now
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
