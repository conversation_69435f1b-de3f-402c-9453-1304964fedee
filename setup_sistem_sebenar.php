<?php
/**
 * Setup Sistem Menggunakan Struktur Database Sebenar
 * Sistem Tempahan Bilik Mesyuarat
 */

// Konfigurasi pangkalan data
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setup Sistem Menggunakan Struktur Database Sebenar</h2>";
    
    // Cipta database
    echo "<p>1. Mencipta pangkalan data '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE $database");
    echo "<p style='color: green;'>✓ Database siap</p>";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    echo "<p>2. Mencipta jadual...</p>";
    
    // Drop jadual lama
    $drop_tables = [
        "DROP TABLE IF EXISTS `ttempahan`",
        "DROP TABLE IF EXISTS `tbilik_penyelaras`",
        "DROP TABLE IF EXISTS `tbilik_mesyuarat`",
        "DROP TABLE IF EXISTS `profile`",
        "DROP TABLE IF EXISTS `pengguna`"
    ];
    
    foreach ($drop_tables as $sql) {
        $pdo->exec($sql);
    }
    echo "<p style='color: green;'>✓ Jadual lama dibuang</p>";
    
    // Cipta jadual profile
    $pdo->exec("CREATE TABLE `profile` (
        `id` int(6) NOT NULL DEFAULT '0',
        `name` varchar(100) NOT NULL,
        `peranan` varchar(255) DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1");
    
    // Insert data profile
    $pdo->exec("INSERT INTO `profile`(`id`,`name`,`peranan`) VALUES
        (1,'Pengguna','Pengguna biasa yang masuk ke sistem untuk membuat tempahan bilik'),
        (2,'Admin','Admin mempunyai semua akses dan boleh capai semua data PTJ'),
        (3,'Penyelaras Bilik Mesyuarat','Penyelaras yang akan menyelenggara maklumat bilik dan melulus tempahan bilik'),
        (4,'Ketua PTJ','Ketua Pusat Tanggungjawab yang mempunyai akses khusus'),
        (9,'Guest','Untuk daftar pengguna baru')");
    echo "<p style='color: green;'>✓ Jadual profile</p>";
    
    // Cipta jadual pengguna (disesuaikan dengan sistem)
    $pdo->exec("CREATE TABLE pengguna (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nokp VARCHAR(12) UNIQUE NOT NULL,
        kata_laluan VARCHAR(255) NOT NULL,
        nama_penuh VARCHAR(100) NOT NULL,
        emel VARCHAR(100) UNIQUE NOT NULL,
        no_telefon VARCHAR(20),
        profile_id INT DEFAULT 1,
        status ENUM('aktif', 'tidak_aktif') DEFAULT 'aktif',
        tarikh_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p style='color: green;'>✓ Jadual pengguna</p>";
    
    // Cipta jadual bilik mesyuarat
    $pdo->exec("CREATE TABLE `tbilik_mesyuarat` (
        `id` int(6) NOT NULL,
        `nama_bilik_mesyuarat` varchar(100) NOT NULL,
        `kapasiti` int(11) NOT NULL,
        `bahagian` int(6) DEFAULT NULL,
        `lokasi` varchar(200) DEFAULT NULL,
        `status` enum('tersedia','tidak_tersedia','penyelenggaraan') DEFAULT 'tersedia',
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1");
    echo "<p style='color: green;'>✓ Jadual bilik mesyuarat</p>";
    
    // Cipta jadual penyelaras bilik
    $pdo->exec("CREATE TABLE `tbilik_penyelaras` (
        `id` int(6) NOT NULL,
        `idbilik_mesyuarat` int(6) DEFAULT NULL,
        `idpenyelaras` int(6) DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1");
    echo "<p style='color: green;'>✓ Jadual penyelaras bilik</p>";
    
    // Cipta jadual tempahan
    $pdo->exec("CREATE TABLE `ttempahan` (
        `id` int(6) NOT NULL AUTO_INCREMENT,
        `idpemohon` int(6) DEFAULT NULL,
        `tarikh_mohon` datetime DEFAULT NULL,
        `tajuk_mesyuarat` varchar(255) DEFAULT NULL,
        `idbilik_mesyuarat` int(6) DEFAULT NULL,
        `tarikh_mula` datetime DEFAULT NULL,
        `tarikh_tamat` datetime DEFAULT NULL,
        `sesi` int(3) DEFAULT NULL,
        `bilangan_peserta` int(11) DEFAULT NULL,
        `pengerusi` varchar(150) DEFAULT NULL,
        `tahun` varchar(4) DEFAULT NULL,
        `kelulusan` varchar(20) DEFAULT 'MENUNGGU',
        `ulasan` varchar(100) DEFAULT NULL,
        `idpegawai_pelulus` int(6) DEFAULT NULL,
        `tarikh_lulus` datetime DEFAULT NULL,
        `batal_tempahan` varchar(5) DEFAULT 'TIDAK',
        `tarikh_kemaskini` datetime DEFAULT NULL,
        `idpegawai_kemaskini` int(6) DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1");
    echo "<p style='color: green;'>✓ Jadual tempahan</p>";
    
    echo "<p>3. Memasukkan data...</p>";
    
    // Insert pengguna dengan kata laluan simple untuk testing
    $password_hash = password_hash('password', PASSWORD_DEFAULT);
    $pdo->exec("INSERT INTO pengguna (nokp, kata_laluan, nama_penuh, emel, profile_id) VALUES
        ('123456789012', '$password_hash', 'Pentadbir Sistem', '<EMAIL>', 2),
        ('234567890123', '$password_hash', 'Ahmad bin Ali', '<EMAIL>', 3),
        ('345678901234', '$password_hash', 'Siti binti Hassan', '<EMAIL>', 1),
        ('456789012345', '$password_hash', 'Dr. Zainal Abidin', '<EMAIL>', 3),
        ('567890123456', '$password_hash', 'Fatimah binti Omar', '<EMAIL>', 1)");
    echo "<p style='color: green;'>✓ Data pengguna (5 pengguna)</p>";
    
    // Insert bilik mesyuarat
    $pdo->exec("INSERT INTO `tbilik_mesyuarat`(`id`,`nama_bilik_mesyuarat`,`kapasiti`,`bahagian`) VALUES 
        (1,'Bilik Mesyuarat Utama',60,3),
        (2,'Bilik Command Centre',25,1),
        (3,'Bilik Mesyuarat Kesihatan Awam',35,1),
        (4,'Bilik Kaunseling Kelompok',8,3),
        (5,'Bilik Mesyuarat Perubatan',20,2),
        (6,'Bilik Mesyuarat Pengurusan',35,3),
        (7,'Bilik Perbincangan Pengarah',10,3),
        (8,'Bilik Mesyuarat Pergigian',25,4),
        (9,'Bilik Sumber Kualiti Perubatan',10,2),
        (10,'Bilik Mesyuarat Farmasi',35,5),
        (12,'TESTING BILIK (HANYA UNTUK PENGUJIAN ICT SAHAJA)',10,3)");
    echo "<p style='color: green;'>✓ Data bilik mesyuarat (11 bilik)</p>";
    
    // Insert penyelaras bilik (gunakan ID pengguna yang baru)
    $pdo->exec("INSERT INTO `tbilik_penyelaras`(`id`,`idbilik_mesyuarat`,`idpenyelaras`) VALUES 
        (1,1,2), (2,2,2), (3,3,2), (4,4,2), (5,5,4), (6,6,2), (7,7,2), (8,8,4), (9,9,4), (10,10,4), (11,12,2)");
    echo "<p style='color: green;'>✓ Data penyelaras bilik</p>";
    
    // Insert sample tempahan
    $pdo->exec("INSERT INTO `ttempahan`(`id`,`idpemohon`,`tarikh_mohon`,`tajuk_mesyuarat`,`idbilik_mesyuarat`,`tarikh_mula`,`tarikh_tamat`,`sesi`,`bilangan_peserta`,`pengerusi`,`tahun`,`kelulusan`,`ulasan`,`idpegawai_pelulus`,`tarikh_lulus`,`batal_tempahan`,`tarikh_kemaskini`,`idpegawai_kemaskini`) VALUES 
        (1,3,NOW(),'Mesyuarat Bulanan Bahagian',1,'2024-01-15 09:00:00','2024-01-15 12:00:00',1,25,'Ketua Bahagian','2024','MENUNGGU',NULL,NULL,NULL,'TIDAK',NOW(),NULL),
        (2,5,NOW(),'Taklimat Keselamatan',3,'2024-01-16 14:00:00','2024-01-16 17:00:00',2,30,'Pegawai Keselamatan','2024','MENUNGGU',NULL,NULL,NULL,'TIDAK',NOW(),NULL),
        (3,3,NOW(),'Mesyuarat Projek ICT',12,'2024-01-17 10:00:00','2024-01-17 15:00:00',3,15,'Ketua ICT','2024','LULUS',NULL,2,'2024-01-10 08:00:00','TIDAK','2024-01-10 08:00:00',2),
        (4,5,NOW(),'Bengkel Latihan',5,'2024-01-18 08:00:00','2024-01-18 17:00:00',3,20,'Koordinator Latihan','2024','MENUNGGU',NULL,NULL,NULL,'TIDAK',NOW(),NULL)");
    echo "<p style='color: green;'>✓ Data tempahan sample (4 tempahan)</p>";
    
    // Enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // Semak data
    echo "<p>4. Menyemak data...</p>";
    $pengguna_count = $pdo->query("SELECT COUNT(*) FROM pengguna")->fetchColumn();
    $bilik_count = $pdo->query("SELECT COUNT(*) FROM tbilik_mesyuarat")->fetchColumn();
    $penyelaras_count = $pdo->query("SELECT COUNT(*) FROM tbilik_penyelaras")->fetchColumn();
    $tempahan_count = $pdo->query("SELECT COUNT(*) FROM ttempahan")->fetchColumn();
    
    echo "<p>Pengguna: $pengguna_count</p>";
    echo "<p>Bilik Mesyuarat: $bilik_count</p>";
    echo "<p>Penyelaras Bilik: $penyelaras_count</p>";
    echo "<p>Tempahan: $tempahan_count</p>";
    
    // Test query penyelaras dan bilik
    echo "<h4>Senarai Penyelaras dan Bilik:</h4>";
    $stmt = $pdo->query("SELECT p.nama_penuh, b.nama_bilik_mesyuarat, pr.name as profile_name
                         FROM tbilik_penyelaras bp
                         JOIN pengguna p ON bp.idpenyelaras = p.id
                         JOIN tbilik_mesyuarat b ON bp.idbilik_mesyuarat = b.id
                         JOIN profile pr ON p.profile_id = pr.id
                         ORDER BY p.nama_penuh, b.nama_bilik_mesyuarat");
    $penyelaras_bilik = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($penyelaras_bilik as $pb) {
        echo "<li><strong>" . htmlspecialchars($pb['nama_penuh']) . "</strong> (" . htmlspecialchars($pb['profile_name']) . ") - " . htmlspecialchars($pb['nama_bilik_mesyuarat']) . "</li>";
    }
    echo "</ul>";
    
    // Test query tempahan menunggu
    echo "<h4>Tempahan Menunggu Kelulusan:</h4>";
    $stmt = $pdo->query("SELECT t.tajuk_mesyuarat, p.nama_penuh as pemohon, b.nama_bilik_mesyuarat, t.tarikh_mula
                         FROM ttempahan t
                         JOIN pengguna p ON t.idpemohon = p.id
                         JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
                         WHERE t.kelulusan = 'MENUNGGU'
                         ORDER BY t.tarikh_mula");
    $tempahan_menunggu = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($tempahan_menunggu as $tm) {
        echo "<li><strong>" . htmlspecialchars($tm['tajuk_mesyuarat']) . "</strong><br>";
        echo "Pemohon: " . htmlspecialchars($tm['pemohon']) . "<br>";
        echo "Bilik: " . htmlspecialchars($tm['nama_bilik_mesyuarat']) . "<br>";
        echo "Tarikh: " . date('d/m/Y H:i', strtotime($tm['tarikh_mula'])) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3 style='color: green;'>✓ Setup sistem sebenar selesai!</h3>";
    
    // Maklumat login
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Maklumat Login:</h4>";
    echo "<p><strong>Admin:</strong> 123456789012 / password</p>";
    echo "<p><strong>Penyelaras Bilik:</strong> 234567890123 / password</p>";
    echo "<p><strong>Pengguna:</strong> 345678901234 / password</p>";
    echo "</div>";
    
    // Pautan
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='log_masuk_simple.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk (Simple)</a>";
    echo "<a href='log_masuk_sebenar.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Log Masuk (Original)</a>";
    echo "<a href='dashboard_pengguna.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Dashboard</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Sistem Sebenar</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f8f9fa; }
        h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h3 { margin-top: 30px; }
        p { margin: 10px 0; }
        ul { background: white; padding: 15px; border-radius: 5px; }
        a { display: inline-block; margin: 5px; }
    </style>
</head>
<body>
</body>
</html>
