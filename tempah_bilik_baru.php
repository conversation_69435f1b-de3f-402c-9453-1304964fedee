<?php
/**
 * Sistem Tempahan Bilik Mesyuarat - Borang Tempahan untuk Bilik Baru
 * Fail ini menunjukkan bagaimana pengguna boleh menempah bilik mesyuarat baru
 */

session_start();

// Sambungan pangkalan data
$host = 'localhost';
$dbname = 'db_tempahan_bm';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Ralat sambungan: " . $e->getMessage());
}

// Dapatkan maklumat bilik jika ID bilik diberikan
$bilik_dipilih = null;
if (isset($_GET['bilik_id'])) {
    $bilik_id = (int)$_GET['bilik_id'];
    $sql = "SELECT * FROM tbilik_mesyuarat WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$bilik_id]);
    $bilik_dipilih = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Proses tempahan jika borang dihantar
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $bilik_id = (int)$_POST['bilik_id'];
    $tarikh = $_POST['tarikh_tempahan'];
    $masa_mula = $_POST['masa_mula'];
    $masa_tamat = $_POST['masa_tamat'];
    $tujuan = $_POST['tujuan'];
    $nama_penempah = $_POST['nama_penempah'];
    $email_penempah = $_POST['email_penempah'];
    $telefon_penempah = $_POST['telefon_penempah'];
    $bilangan_peserta = (int)$_POST['bilangan_peserta'];
    
    // Semak ketersediaan bilik
    $sql_check = "SELECT COUNT(*) FROM ttempahan 
                  WHERE idbilik_mesyuarat = ? 
                  AND tarikh_tempahan = ? 
                  AND ((masa_mula <= ? AND masa_tamat > ?) 
                       OR (masa_mula < ? AND masa_tamat >= ?)
                       OR (masa_mula >= ? AND masa_tamat <= ?))
                  AND status != 'DIBATAL'";
    
    $stmt_check = $pdo->prepare($sql_check);
    $stmt_check->execute([
        $bilik_id, $tarikh, 
        $masa_mula, $masa_mula,
        $masa_tamat, $masa_tamat,
        $masa_mula, $masa_tamat
    ]);
    
    if ($stmt_check->fetchColumn() == 0) {
        // Bilik tersedia, buat tempahan
        $sql_insert = "INSERT INTO ttempahan 
                       (idbilik_mesyuarat, tarikh_tempahan, masa_mula, masa_tamat, 
                        tujuan, nama_penempah, email_penempah, telefon_penempah, 
                        bilangan_peserta, status, tarikh_tempahan_dibuat) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'MENUNGGU', NOW())";
        
        $stmt_insert = $pdo->prepare($sql_insert);
        $result = $stmt_insert->execute([
            $bilik_id, $tarikh, $masa_mula, $masa_tamat,
            $tujuan, $nama_penempah, $email_penempah, $telefon_penempah,
            $bilangan_peserta
        ]);
        
        if ($result) {
            $tempahan_id = $pdo->lastInsertId();
            $success_message = "Tempahan berjaya dibuat! ID Tempahan: " . $tempahan_id;
        } else {
            $error_message = "Ralat semasa membuat tempahan.";
        }
    } else {
        $error_message = "Bilik tidak tersedia pada masa yang dipilih.";
    }
}

// Dapatkan senarai semua bilik untuk dropdown
$sql_bilik = "SELECT * FROM tbilik_mesyuarat ORDER BY bahagian, nama_bilik_mesyuarat";
$stmt_bilik = $pdo->prepare($sql_bilik);
$stmt_bilik->execute();
$senarai_bilik = $stmt_bilik->fetchAll(PDO::FETCH_ASSOC);

// Fungsi untuk mendapatkan nama bahagian
function getNamaBahagian($bahagian_id) {
    $bahagian = [
        1 => 'Pentadbiran & Kesihatan Awam',
        2 => 'Perubatan',
        3 => 'Pengurusan',
        4 => 'Pergigian',
        5 => 'Farmasi',
        6 => 'Teknologi Maklumat dan Komunikasi',
        7 => 'Penyelidikan dan Pembangunan',
        8 => 'Latihan dan Pembangunan'
    ];
    return isset($bahagian[$bahagian_id]) ? $bahagian[$bahagian_id] : 'Tidak Diketahui';
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tempah Bilik Mesyuarat</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        textarea { height: 100px; resize: vertical; }
        .btn { background-color: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn:hover { background-color: #0056b3; }
        .btn-secondary { background-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .bilik-info { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .row { display: flex; gap: 15px; }
        .col { flex: 1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tempah Bilik Mesyuarat</h1>
        
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success"><?= $success_message ?></div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger"><?= $error_message ?></div>
        <?php endif; ?>
        
        <?php if ($bilik_dipilih): ?>
            <div class="bilik-info">
                <h3>Bilik Dipilih: <?= htmlspecialchars($bilik_dipilih['nama_bilik_mesyuarat']) ?></h3>
                <p><strong>Kapasiti:</strong> <?= $bilik_dipilih['kapasiti'] ?> orang</p>
                <p><strong>Bahagian:</strong> <?= getNamaBahagian($bilik_dipilih['bahagian']) ?></p>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="bilik_id">Pilih Bilik Mesyuarat:</label>
                <select name="bilik_id" id="bilik_id" required onchange="updateBilikInfo()">
                    <option value="">-- Pilih Bilik --</option>
                    <?php 
                    $current_bahagian = null;
                    foreach ($senarai_bilik as $bilik): 
                        if ($current_bahagian != $bilik['bahagian']):
                            if ($current_bahagian !== null) echo "</optgroup>";
                            echo "<optgroup label='" . getNamaBahagian($bilik['bahagian']) . "'>";
                            $current_bahagian = $bilik['bahagian'];
                        endif;
                    ?>
                        <option value="<?= $bilik['id'] ?>" 
                                data-kapasiti="<?= $bilik['kapasiti'] ?>"
                                data-bahagian="<?= getNamaBahagian($bilik['bahagian']) ?>"
                                <?= ($bilik_dipilih && $bilik['id'] == $bilik_dipilih['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?> (<?= $bilik['kapasiti'] ?> orang)
                        </option>
                    <?php endforeach; ?>
                    <?php if ($current_bahagian !== null) echo "</optgroup>"; ?>
                </select>
            </div>
            
            <div id="bilik-info-display" style="display: none;" class="bilik-info">
                <h4 id="bilik-nama"></h4>
                <p><strong>Kapasiti:</strong> <span id="bilik-kapasiti"></span> orang</p>
                <p><strong>Bahagian:</strong> <span id="bilik-bahagian"></span></p>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="tarikh_tempahan">Tarikh Tempahan:</label>
                        <input type="date" name="tarikh_tempahan" id="tarikh_tempahan" required min="<?= date('Y-m-d') ?>">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="bilangan_peserta">Bilangan Peserta:</label>
                        <input type="number" name="bilangan_peserta" id="bilangan_peserta" required min="1">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="masa_mula">Masa Mula:</label>
                        <input type="time" name="masa_mula" id="masa_mula" required>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="masa_tamat">Masa Tamat:</label>
                        <input type="time" name="masa_tamat" id="masa_tamat" required>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="tujuan">Tujuan Mesyuarat:</label>
                <textarea name="tujuan" id="tujuan" required placeholder="Nyatakan tujuan atau agenda mesyuarat"></textarea>
            </div>
            
            <div class="form-group">
                <label for="nama_penempah">Nama Penempah:</label>
                <input type="text" name="nama_penempah" id="nama_penempah" required>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="email_penempah">Email:</label>
                        <input type="email" name="email_penempah" id="email_penempah" required>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="telefon_penempah">No. Telefon:</label>
                        <input type="tel" name="telefon_penempah" id="telefon_penempah" required>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn">Hantar Tempahan</button>
                <a href="senarai_bilik_baru.php" class="btn btn-secondary" style="text-decoration: none; margin-left: 10px;">Kembali ke Senarai Bilik</a>
            </div>
        </form>
    </div>
    
    <script>
        function updateBilikInfo() {
            const select = document.getElementById('bilik_id');
            const selectedOption = select.options[select.selectedIndex];
            const infoDiv = document.getElementById('bilik-info-display');
            
            if (selectedOption.value) {
                document.getElementById('bilik-nama').textContent = selectedOption.text.split(' (')[0];
                document.getElementById('bilik-kapasiti').textContent = selectedOption.dataset.kapasiti;
                document.getElementById('bilik-bahagian').textContent = selectedOption.dataset.bahagian;
                infoDiv.style.display = 'block';
            } else {
                infoDiv.style.display = 'none';
            }
        }
        
        // Validasi masa
        document.getElementById('masa_tamat').addEventListener('change', function() {
            const masaMula = document.getElementById('masa_mula').value;
            const masaTamat = this.value;
            
            if (masaMula && masaTamat && masaTamat <= masaMula) {
                alert('Masa tamat mestilah selepas masa mula!');
                this.value = '';
            }
        });
    </script>
</body>
</html>
