<?php
/**
 * Debug Dropdown - Semak Data Bahagian, Jawatan, Gred
 */

require_once 'config/sistem_config.php';

echo "<h2>Debug Dropdown - Sistem Tempahan Bilik Mesyuarat</h2>";

try {
    // Test sambungan database
    echo "<h3>1. Test Sambungan Database</h3>";
    $test = $db->fetch("SELECT 1 as test");
    if ($test['test'] == 1) {
        echo "<p style='color: green;'>✓ Sambungan database OK</p>";
    } else {
        echo "<p style='color: red;'>✗ Sambungan database gagal</p>";
    }
    
    // Semak jadual wujud
    echo "<h3>2. Semak Jadual Wujud</h3>";
    $tables = $db->fetchAll("SHOW TABLES");
    $table_names = array_column($tables, 'Tables_in_sistem_tempahan_bilik');
    
    $required_tables = ['tbahagian', 'tjawatan', 'tgred'];
    foreach ($required_tables as $table) {
        if (in_array($table, $table_names)) {
            echo "<p style='color: green;'>✓ Jadual '$table' wujud</p>";
        } else {
            echo "<p style='color: red;'>✗ Jadual '$table' TIDAK wujud</p>";
        }
    }
    
    // Semak data bahagian
    echo "<h3>3. Data Bahagian</h3>";
    try {
        $sql_bahagian = "SELECT id, bahagian FROM tbahagian ORDER BY bahagian";
        $senarai_bahagian = $db->fetchAll($sql_bahagian);
        
        echo "<p>Jumlah bahagian: " . count($senarai_bahagian) . "</p>";
        
        if (count($senarai_bahagian) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Bahagian</th></tr>";
            foreach ($senarai_bahagian as $bahagian) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($bahagian['id']) . "</td>";
                echo "<td>" . htmlspecialchars($bahagian['bahagian']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ Tiada data bahagian dijumpai!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Ralat query bahagian: " . $e->getMessage() . "</p>";
    }
    
    // Semak data jawatan
    echo "<h3>4. Data Jawatan</h3>";
    try {
        $sql_jawatan = "SELECT id, jawatan FROM tjawatan ORDER BY jawatan LIMIT 10";
        $senarai_jawatan = $db->fetchAll($sql_jawatan);
        
        echo "<p>Jumlah jawatan (10 pertama): " . count($senarai_jawatan) . "</p>";
        
        if (count($senarai_jawatan) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Jawatan</th></tr>";
            foreach ($senarai_jawatan as $jawatan) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($jawatan['id']) . "</td>";
                echo "<td>" . htmlspecialchars($jawatan['jawatan']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ Tiada data jawatan dijumpai!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Ralat query jawatan: " . $e->getMessage() . "</p>";
    }
    
    // Semak data gred
    echo "<h3>5. Data Gred</h3>";
    try {
        $sql_gred = "SELECT id, gred FROM tgred ORDER BY gred LIMIT 10";
        $senarai_gred = $db->fetchAll($sql_gred);
        
        echo "<p>Jumlah gred (10 pertama): " . count($senarai_gred) . "</p>";
        
        if (count($senarai_gred) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Gred</th></tr>";
            foreach ($senarai_gred as $gred) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($gred['id']) . "</td>";
                echo "<td>" . htmlspecialchars($gred['gred']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ Tiada data gred dijumpai!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Ralat query gred: " . $e->getMessage() . "</p>";
    }
    
    // Test query yang sama seperti dalam daftar.php
    echo "<h3>6. Test Query Sama Seperti daftar.php</h3>";
    try {
        $sql_bahagian = "SELECT id, bahagian FROM tbahagian ORDER BY bahagian";
        $senarai_bahagian = $db->fetchAll($sql_bahagian);
        
        $sql_jawatan = "SELECT id, jawatan FROM tjawatan ORDER BY jawatan";
        $senarai_jawatan = $db->fetchAll($sql_jawatan);
        
        $sql_gred = "SELECT id, gred FROM tgred ORDER BY gred";
        $senarai_gred = $db->fetchAll($sql_gred);
        
        echo "<p>Bahagian: " . count($senarai_bahagian) . " rekod</p>";
        echo "<p>Jawatan: " . count($senarai_jawatan) . " rekod</p>";
        echo "<p>Gred: " . count($senarai_gred) . " rekod</p>";
        
        // Test dropdown HTML
        echo "<h4>Test Dropdown HTML:</h4>";
        echo "<select>";
        echo "<option value=''>Pilih Bahagian</option>";
        foreach ($senarai_bahagian as $bahagian) {
            echo "<option value='" . $bahagian['id'] . "'>" . htmlspecialchars($bahagian['bahagian']) . "</option>";
        }
        echo "</select>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Ralat test query: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ Ralat Utama:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><br>";
echo "<a href='daftar.php'>Kembali ke Daftar</a> | ";
echo "<a href='insert_data.php'>Insert Data</a> | ";
echo "<a href='setup_database.php'>Setup Database</a>";
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Dropdown</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
</body>
</html>
