<?php
require_once '../config/config.php';
require_once '../classes/Room.php';
require_once '../classes/Booking.php';
require_once '../classes/User.php';

requireAdmin();

$room = new Room();
$booking = new Booking();
$user = new User();

// Get dashboard statistics
$total_rooms = count($room->getAllRooms(false));
$active_rooms = count($room->getAllRooms(true));
$total_users = count($user->getAllUsers());
$booking_stats = $booking->getBookingStats();

// Get recent bookings
$recent_bookings = $booking->getAllBookings(null, null, null);
$recent_bookings = array_slice($recent_bookings, 0, 10);

// Get pending bookings
$pending_bookings = $booking->getAllBookings('pending');

$page_title = 'Admin Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h2>
        <div class="btn-group">
            <a href="rooms.php" class="btn btn-primary">
                <i class="fas fa-door-open"></i> Manage Rooms
            </a>
            <a href="../my_bookings.php" class="btn btn-success">
                <i class="fas fa-calendar-check"></i> Manage Bookings
            </a>
            <a href="../kelola_pengguna.php" class="btn btn-info">
                <i class="fas fa-users"></i> Manage Users
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $total_rooms; ?></h4>
                            <p class="mb-0">Total Rooms</p>
                        </div>
                        <i class="fas fa-door-open fa-2x opacity-75"></i>
                    </div>
                    <div class="mt-2">
                        <small><?php echo $active_rooms; ?> active rooms</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $booking_stats['total']; ?></h4>
                            <p class="mb-0">Total Bookings</p>
                        </div>
                        <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                    </div>
                    <div class="mt-2">
                        <small><?php echo $booking_stats['month']; ?> this month</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $booking_stats['pending']; ?></h4>
                            <p class="mb-0">Pending Bookings</p>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                    <div class="mt-2">
                        <small>Require attention</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?php echo $total_users; ?></h4>
                            <p class="mb-0">Total Users</p>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                    <div class="mt-2">
                        <small>Registered users</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pending Bookings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-clock"></i> Pending Bookings</h5>
                    <a href="../my_bookings.php?status=pending" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_bookings)): ?>
                        <p class="text-muted text-center">No pending bookings</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Room</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($pending_bookings, 0, 5) as $booking_item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($booking_item['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($booking_item['room_name']); ?></td>
                                            <td>
                                                <?php echo formatDate($booking_item['booking_date']); ?><br>
                                                <small class="text-muted">
                                                    <?php echo formatTime($booking_item['start_time']) . '-' . formatTime($booking_item['end_time']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-success" onclick="confirmBooking(<?php echo $booking_item['id']; ?>)" title="Confirm">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <a href="../my_bookings.php?id=<?php echo $booking_item['id']; ?>"
                                                       class="btn btn-outline-primary" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Recent Bookings</h5>
                    <a href="../my_bookings.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_bookings)): ?>
                        <p class="text-muted text-center">No recent bookings</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Room</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($recent_bookings, 0, 5) as $booking_item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($booking_item['full_name']); ?></td>
                                            <td><?php echo htmlspecialchars($booking_item['room_name']); ?></td>
                                            <td>
                                                <?php echo formatDate($booking_item['booking_date']); ?><br>
                                                <small class="text-muted">
                                                    <?php echo formatTime($booking_item['start_time']) . '-' . formatTime($booking_item['end_time']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $booking_item['status'] == 'confirmed' ? 'success' : 
                                                        ($booking_item['status'] == 'pending' ? 'warning' : 
                                                        ($booking_item['status'] == 'cancelled' ? 'danger' : 'info')); 
                                                ?>">
                                                    <?php echo ucfirst($booking_item['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="rooms.php?action=add" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add New Room
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="../kelulusan_tempahan.php" class="btn btn-warning">
                                    <i class="fas fa-clock"></i> Review Pending
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="../kelola_pengguna.php" class="btn btn-info">
                                    <i class="fas fa-user-plus"></i> Manage Users
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="../kalendar.php" class="btn btn-secondary">
                                    <i class="fas fa-chart-bar"></i> View Calendar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmBooking(bookingId) {
    if (confirm('Are you sure you want to confirm this booking?')) {
        fetch('../ajax/confirm_booking.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                booking_id: bookingId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Booking confirmed successfully', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Failed to confirm booking: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred', 'danger');
        });
    }
}
</script>

<?php include '../includes/footer.php'; ?>
