<?php
/**
 * Sistem Tempahan Bilik Mesyuarat - Senarai Bilik Baru
 * Fail ini menunjukkan bagaimana sistem boleh menggunakan bilik mesyuarat baru
 */

// Sambungan pangkalan data (sesuaikan dengan tetapan anda)
$host = 'localhost';
$dbname = 'db_tempahan_bm'; // Nama pangkalan data anda
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Ralat sambungan: " . $e->getMessage());
}

// Fungsi untuk mendapatkan senarai bilik mengikut bahagian
function getBilikByBahagian($pdo, $bahagian_id = null) {
    if ($bahagian_id) {
        $sql = "SELECT * FROM tbilik_mesyuarat WHERE bahagian = ? ORDER BY nama_bilik_mesyuarat";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$bahagian_id]);
    } else {
        $sql = "SELECT * FROM tbilik_mesyuarat ORDER BY bahagian, nama_bilik_mesyuarat";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fungsi untuk mendapatkan nama bahagian
function getNamaBahagian($bahagian_id) {
    $bahagian = [
        1 => 'Pentadbiran & Kesihatan Awam',
        2 => 'Perubatan',
        3 => 'Pengurusan',
        4 => 'Pergigian',
        5 => 'Farmasi',
        6 => 'Teknologi Maklumat dan Komunikasi',
        7 => 'Penyelidikan dan Pembangunan',
        8 => 'Latihan dan Pembangunan'
    ];
    return isset($bahagian[$bahagian_id]) ? $bahagian[$bahagian_id] : 'Tidak Diketahui';
}

// Fungsi untuk memeriksa ketersediaan bilik
function checkKetersediaan($pdo, $bilik_id, $tarikh, $masa_mula, $masa_tamat) {
    $sql = "SELECT COUNT(*) FROM ttempahan 
            WHERE idbilik_mesyuarat = ? 
            AND tarikh_tempahan = ? 
            AND ((masa_mula <= ? AND masa_tamat > ?) 
                 OR (masa_mula < ? AND masa_tamat >= ?)
                 OR (masa_mula >= ? AND masa_tamat <= ?))
            AND status != 'DIBATAL'";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $bilik_id, $tarikh, 
        $masa_mula, $masa_mula,
        $masa_tamat, $masa_tamat,
        $masa_mula, $masa_tamat
    ]);
    
    return $stmt->fetchColumn() == 0;
}

?>
<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Senarai Bilik Mesyuarat Baru</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .bahagian { margin-bottom: 30px; }
        .bahagian h2 { 
            background-color: #f0f0f0; 
            padding: 10px; 
            border-left: 5px solid #007bff; 
        }
        .bilik-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); 
            gap: 15px; 
        }
        .bilik-card { 
            border: 1px solid #ddd; 
            padding: 15px; 
            border-radius: 5px; 
            background-color: #f9f9f9; 
        }
        .bilik-card h4 { margin-top: 0; color: #333; }
        .kapasiti { color: #666; font-size: 14px; }
        .btn-tempah { 
            background-color: #28a745; 
            color: white; 
            padding: 8px 15px; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer; 
            margin-top: 10px; 
        }
        .btn-tempah:hover { background-color: #218838; }
        .filter { margin-bottom: 20px; }
        .filter select { padding: 8px; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sistem Tempahan Bilik Mesyuarat - Senarai Bilik Baru</h1>
        
        <div class="filter">
            <label for="bahagian">Tapis mengikut Bahagian:</label>
            <select id="bahagian" onchange="filterBahagian()">
                <option value="">Semua Bahagian</option>
                <?php for($i = 1; $i <= 8; $i++): ?>
                    <option value="<?= $i ?>"><?= getNamaBahagian($i) ?></option>
                <?php endfor; ?>
            </select>
        </div>

        <?php
        // Dapatkan parameter bahagian jika ada
        $bahagian_filter = isset($_GET['bahagian']) ? (int)$_GET['bahagian'] : null;
        
        if ($bahagian_filter) {
            // Tunjukkan bilik untuk bahagian tertentu sahaja
            $bilik_list = getBilikByBahagian($pdo, $bahagian_filter);
            echo "<div class='bahagian'>";
            echo "<h2>" . getNamaBahagian($bahagian_filter) . "</h2>";
            echo "<div class='bilik-grid'>";
            
            foreach ($bilik_list as $bilik) {
                echo "<div class='bilik-card'>";
                echo "<h4>" . htmlspecialchars($bilik['nama_bilik_mesyuarat']) . "</h4>";
                echo "<div class='kapasiti'>Kapasiti: " . $bilik['kapasiti'] . " orang</div>";
                echo "<div class='kapasiti'>ID Bilik: " . $bilik['id'] . "</div>";
                echo "<button class='btn-tempah' onclick='tempahBilik(" . $bilik['id'] . ")'>Tempah Bilik</button>";
                echo "</div>";
            }
            
            echo "</div></div>";
        } else {
            // Tunjukkan semua bilik mengikut bahagian
            for ($bahagian_id = 1; $bahagian_id <= 8; $bahagian_id++) {
                $bilik_list = getBilikByBahagian($pdo, $bahagian_id);
                
                if (!empty($bilik_list)) {
                    echo "<div class='bahagian'>";
                    echo "<h2>" . getNamaBahagian($bahagian_id) . " (" . count($bilik_list) . " bilik)</h2>";
                    echo "<div class='bilik-grid'>";
                    
                    foreach ($bilik_list as $bilik) {
                        echo "<div class='bilik-card'>";
                        echo "<h4>" . htmlspecialchars($bilik['nama_bilik_mesyuarat']) . "</h4>";
                        echo "<div class='kapasiti'>Kapasiti: " . $bilik['kapasiti'] . " orang</div>";
                        echo "<div class='kapasiti'>ID Bilik: " . $bilik['id'] . "</div>";
                        echo "<button class='btn-tempah' onclick='tempahBilik(" . $bilik['id'] . ")'>Tempah Bilik</button>";
                        echo "</div>";
                    }
                    
                    echo "</div></div>";
                }
            }
        }
        ?>
    </div>

    <script>
        function filterBahagian() {
            const bahagian = document.getElementById('bahagian').value;
            if (bahagian) {
                window.location.href = '?bahagian=' + bahagian;
            } else {
                window.location.href = window.location.pathname;
            }
        }

        function tempahBilik(bilikId) {
            // Redirect ke halaman tempahan dengan ID bilik
            window.location.href = 'tempah_bilik.php?bilik_id=' + bilikId;
        }
    </script>
</body>
</html>
