<?php
require_once 'config/config.php';
require_once 'classes/Booking.php';

requireLogin();

$booking = new Booking();

// Handle booking actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $booking_id = (int)($_POST['booking_id'] ?? 0);

    if ($action === 'cancel' && $booking_id) {
        $result = $booking->cancelBooking($booking_id, $_SESSION['user_id']);
        $message = $result['message'];
        $message_type = $result['success'] ? 'success' : 'danger';
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Get user bookings
$user_bookings = $booking->getUserBookings($_SESSION['user_id']);

// Apply filters
if ($status_filter) {
    $user_bookings = array_filter($user_bookings, function($b) use ($status_filter) {
        return $b['status'] === $status_filter;
    });
}

if ($date_from) {
    $user_bookings = array_filter($user_bookings, function($b) use ($date_from) {
        return $b['booking_date'] >= $date_from;
    });
}

if ($date_to) {
    $user_bookings = array_filter($user_bookings, function($b) use ($date_to) {
        return $b['booking_date'] <= $date_to;
    });
}

$page_title = 'My Bookings';
include 'includes/header.php';
?>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-calendar-alt"></i> My Bookings</h2>
        <a href="rooms.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Booking
        </a>
    </div>

    <?php if (isset($message)): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_GET['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter"></i> Filter Bookings</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="confirmed" <?php echo $status_filter === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="my_bookings.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bookings List -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> Booking History</h5>
        </div>
        <div class="card-body">
            <?php if (empty($user_bookings)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                    <h4>No bookings found</h4>
                    <p class="text-muted">You haven't made any bookings yet or no bookings match your filter criteria.</p>
                    <a href="rooms.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Make Your First Booking
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Room</th>
                                <th>Date & Time</th>
                                <th>Purpose</th>
                                <th>Attendees</th>
                                <th>Cost</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_bookings as $booking_item): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($booking_item['room_name']); ?></strong><br>
                                        <small class="text-muted">
                                            <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($booking_item['location']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <strong><?php echo formatDate($booking_item['booking_date']); ?></strong><br>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> 
                                            <?php echo formatTime($booking_item['start_time']) . ' - ' . formatTime($booking_item['end_time']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($booking_item['purpose']); ?>
                                        <?php if ($booking_item['notes']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($booking_item['notes']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <i class="fas fa-users"></i> <?php echo $booking_item['attendees']; ?>
                                    </td>
                                    <td>
                                        <?php echo formatCurrency($booking_item['total_cost']); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $booking_item['status'] == 'confirmed' ? 'success' : 
                                                ($booking_item['status'] == 'pending' ? 'warning' : 
                                                ($booking_item['status'] == 'cancelled' ? 'danger' : 'info')); 
                                        ?>">
                                            <?php echo ucfirst($booking_item['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="booking_details.php?id=<?php echo $booking_item['id']; ?>" 
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if ($booking_item['status'] == 'pending' || $booking_item['status'] == 'confirmed'): ?>
                                                <?php if (strtotime($booking_item['booking_date'] . ' ' . $booking_item['start_time']) > time()): ?>
                                                    <a href="edit_booking.php?id=<?php echo $booking_item['id']; ?>" 
                                                       class="btn btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            title="Cancel" onclick="cancelBooking(<?php echo $booking_item['id']; ?>)">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Cancel Booking Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Booking</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this booking?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No, Keep Booking</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="cancel">
                    <input type="hidden" name="booking_id" id="cancelBookingId">
                    <button type="submit" class="btn btn-danger">Yes, Cancel Booking</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function cancelBooking(bookingId) {
    document.getElementById('cancelBookingId').value = bookingId;
    const modal = new bootstrap.Modal(document.getElementById('cancelModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
