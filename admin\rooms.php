<?php
require_once '../config/config.php';
require_once '../classes/Room.php';

requireAdmin();

$room = new Room();
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $room_data = [
            'room_name' => sanitizeInput($_POST['room_name'] ?? ''),
            'capacity' => (int)($_POST['capacity'] ?? 0),
            'location' => sanitizeInput($_POST['location'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? ''),
            'facilities' => $_POST['facilities'] ?? [],
            'hourly_rate' => (float)($_POST['hourly_rate'] ?? 0),
            'image_url' => sanitizeInput($_POST['image_url'] ?? '')
        ];

        if ($action === 'add') {
            $result = $room->createRoom($room_data);
        } else {
            $room_id = (int)($_POST['room_id'] ?? 0);
            $result = $room->updateRoom($room_id, $room_data);
        }

        if ($result['success']) {
            $success = $result['message'];
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'toggle_status') {
        $room_id = (int)($_POST['room_id'] ?? 0);
        $result = $room->toggleRoomStatus($room_id);
        if ($result['success']) {
            $success = $result['message'];
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $room_id = (int)($_POST['room_id'] ?? 0);
        $result = $room->deleteRoom($room_id);
        if ($result['success']) {
            $success = $result['message'];
        } else {
            $error = $result['message'];
        }
    }
}

// Get all rooms
$rooms = $room->getAllRooms(false);

// Get room for editing
$edit_room = null;
if (isset($_GET['edit'])) {
    $edit_room = $room->getRoomById((int)$_GET['edit']);
}

$page_title = 'Manage Rooms';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-door-open"></i> Manage Rooms</h2>
        <div class="btn-group">
            <a href="dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#roomModal">
                <i class="fas fa-plus"></i> Add New Room
            </button>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Rooms List -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> All Rooms</h5>
        </div>
        <div class="card-body">
            <?php if (empty($rooms)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-door-open fa-3x text-muted mb-3"></i>
                    <h5>No rooms found</h5>
                    <p class="text-muted">Start by adding your first meeting room</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Room Name</th>
                                <th>Location</th>
                                <th>Capacity</th>
                                <th>Rate/Hour</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($rooms as $room_data): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($room_data['room_name']); ?></strong>
                                        <?php if ($room_data['description']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($room_data['description'], 0, 50)) . '...'; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($room_data['location']); ?></td>
                                    <td>
                                        <i class="fas fa-users"></i> <?php echo $room_data['capacity']; ?>
                                    </td>
                                    <td><?php echo formatCurrency($room_data['hourly_rate']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $room_data['is_active'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $room_data['is_active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editRoom(<?php echo htmlspecialchars(json_encode($room_data)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="room_id" value="<?php echo $room_data['id']; ?>">
                                                <button type="submit" class="btn btn-outline-<?php echo $room_data['is_active'] ? 'warning' : 'success'; ?>" 
                                                        title="<?php echo $room_data['is_active'] ? 'Deactivate' : 'Activate'; ?>">
                                                    <i class="fas fa-<?php echo $room_data['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                            </form>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteRoom(<?php echo $room_data['id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Room Modal -->
<div class="modal fade" id="roomModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="roomModalTitle">Add New Room</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="roomForm">
                <div class="modal-body">
                    <input type="hidden" name="action" id="roomAction" value="add">
                    <input type="hidden" name="room_id" id="roomId">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="room_name" class="form-label">Room Name *</label>
                            <input type="text" class="form-control" id="room_name" name="room_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="capacity" class="form-label">Capacity *</label>
                            <input type="number" class="form-control" id="capacity" name="capacity" min="1" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="location" class="form-label">Location *</label>
                            <input type="text" class="form-control" id="location" name="location" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="hourly_rate" class="form-label">Hourly Rate (RM) *</label>
                            <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" 
                                   min="0" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image_url" class="form-label">Image URL</label>
                        <input type="url" class="form-control" id="image_url" name="image_url">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Facilities</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Projector" id="facility1">
                                    <label class="form-check-label" for="facility1">Projector</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Whiteboard" id="facility2">
                                    <label class="form-check-label" for="facility2">Whiteboard</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Video Conferencing" id="facility3">
                                    <label class="form-check-label" for="facility3">Video Conferencing</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Air Conditioning" id="facility4">
                                    <label class="form-check-label" for="facility4">Air Conditioning</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Sound System" id="facility5">
                                    <label class="form-check-label" for="facility5">Sound System</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="TV Screen" id="facility6">
                                    <label class="form-check-label" for="facility6">TV Screen</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Catering Setup" id="facility7">
                                    <label class="form-check-label" for="facility7">Catering Setup</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="facilities[]" value="Flipcharts" id="facility8">
                                    <label class="form-check-label" for="facility8">Flipcharts</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="roomSubmitBtn">Add Room</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Room</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this room?</p>
                <p class="text-danger">This action cannot be undone and will fail if the room has existing bookings.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="room_id" id="deleteRoomId">
                    <button type="submit" class="btn btn-danger">Delete Room</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editRoom(roomData) {
    document.getElementById('roomModalTitle').textContent = 'Edit Room';
    document.getElementById('roomAction').value = 'edit';
    document.getElementById('roomId').value = roomData.id;
    document.getElementById('roomSubmitBtn').textContent = 'Update Room';
    
    document.getElementById('room_name').value = roomData.room_name;
    document.getElementById('capacity').value = roomData.capacity;
    document.getElementById('location').value = roomData.location;
    document.getElementById('hourly_rate').value = roomData.hourly_rate;
    document.getElementById('description').value = roomData.description || '';
    document.getElementById('image_url').value = roomData.image_url || '';
    
    // Clear all facility checkboxes
    document.querySelectorAll('input[name="facilities[]"]').forEach(cb => cb.checked = false);
    
    // Check facilities
    if (roomData.facilities) {
        const facilities = JSON.parse(roomData.facilities);
        facilities.forEach(facility => {
            const checkbox = document.querySelector(`input[value="${facility}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }
    
    const modal = new bootstrap.Modal(document.getElementById('roomModal'));
    modal.show();
}

function deleteRoom(roomId) {
    document.getElementById('deleteRoomId').value = roomId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Reset form when modal is closed
document.getElementById('roomModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('roomForm').reset();
    document.getElementById('roomModalTitle').textContent = 'Add New Room';
    document.getElementById('roomAction').value = 'add';
    document.getElementById('roomSubmitBtn').textContent = 'Add Room';
});
</script>

<?php include '../includes/footer.php'; ?>
