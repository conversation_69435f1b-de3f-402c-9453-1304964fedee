<?php
/**
 * Halaman Pendaftaran Pengguna
 * Sistem Tempahan Bilik Mesyuarat
 */

require_once 'config/sistem_config.php';

// Jika pengguna sudah log masuk, arahkan ke halaman utama
if (semakLogin()) {
    arahkanKe(URL_SISTEM . '/dashboard.php');
}

$mesej_ralat = '';
$mesej_kejayaan = '';

// Proses pendaftaran
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nokp = bersihkanInput($_POST['nokp']);
    $kata_laluan = $_POST['kata_laluan'];
    $sahkan_kata_laluan = $_POST['sahkan_kata_laluan'];
    $nama_penuh = bersihkanInput($_POST['nama_penuh']);
    $emel = bersihkanInput($_POST['emel']);
    $no_telefon = bersihkanInput($_POST['no_telefon']);
    $bahagian_id = (int)$_POST['bahagian_id'];
    $unit_id = !empty($_POST['unit_id']) ? (int)$_POST['unit_id'] : null;
    $jawatan_id = (int)$_POST['jawatan_id'];
    $gred_id = (int)$_POST['gred_id'];

    // Validasi input
    if (empty($nokp) || empty($kata_laluan) || empty($nama_penuh) || empty($emel) ||
        empty($bahagian_id) || empty($jawatan_id) || empty($gred_id)) {
        $mesej_ralat = 'Sila lengkapkan semua medan yang diperlukan.';
    } elseif (empty($nokp) || !ctype_digit($nokp)) {
        $mesej_ralat = 'No. KP mestilah nombor sahaja.';
    } elseif (strlen($kata_laluan) < 6) {
        $mesej_ralat = 'Kata laluan mestilah sekurang-kurangnya 6 aksara.';
    } elseif ($kata_laluan !== $sahkan_kata_laluan) {
        $mesej_ralat = 'Kata laluan dan pengesahan kata laluan tidak sepadan.';
    } elseif (!filter_var($emel, FILTER_VALIDATE_EMAIL)) {
        $mesej_ralat = 'Format emel tidak sah.';
    } else {
        try {
            // Semak jika No. KP sudah wujud
            $sql_check = "SELECT id FROM pengguna WHERE nokp = ? OR emel = ?";
            $existing = $db->fetch($sql_check, [$nokp, $emel]);

            if ($existing) {
                $mesej_ralat = 'No. KP atau emel sudah digunakan.';
            } else {
                // Hash kata laluan
                $kata_laluan_hash = password_hash($kata_laluan, PASSWORD_DEFAULT);

                // Masukkan pengguna baru
                $sql_insert = "INSERT INTO pengguna (nokp, kata_laluan, nama_penuh, emel, no_telefon,
                                                   bahagian_id, unit_id, jawatan_id, gred_id, peranan)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pengguna')";

                $result = $db->query($sql_insert, [
                    $nokp, $kata_laluan_hash, $nama_penuh, $emel,
                    $no_telefon, $bahagian_id, $unit_id, $jawatan_id, $gred_id
                ]);

                if ($result) {
                    $mesej_kejayaan = 'Akaun berjaya didaftarkan! Sila log masuk.';
                    // Kosongkan borang
                    $_POST = [];
                } else {
                    $mesej_ralat = 'Ralat semasa mendaftarkan akaun. Sila cuba lagi.';
                }
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat sistem. Sila cuba lagi.';
        }
    }
}

// Dapatkan senarai bahagian, unit, jawatan dan gred
try {
    $sql_bahagian = "SELECT id, bahagian FROM tbahagian ORDER BY bahagian";
    $senarai_bahagian = $db->fetchAll($sql_bahagian);

    $sql_unit = "SELECT id, unit, idbahagian FROM tunit ORDER BY unit";
    $senarai_unit = $db->fetchAll($sql_unit);

    $sql_jawatan = "SELECT id, jawatan FROM tjawatan ORDER BY jawatan";
    $senarai_jawatan = $db->fetchAll($sql_jawatan);

    $sql_gred = "SELECT id, gred FROM tgred ORDER BY gred";
    $senarai_gred = $db->fetchAll($sql_gred);
} catch (Exception $e) {
    $senarai_bahagian = [];
    $senarai_unit = [];
    $senarai_jawatan = [];
    $senarai_gred = [];
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar Akaun - <?= NAMA_SISTEM ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-container">
                    <div class="register-header">
                        <i class="bi bi-person-plus fs-1 mb-3"></i>
                        <h3 class="mb-0">Daftar Akaun Baru</h3>
                        <p class="mb-0 mt-2"><?= NAMA_SISTEM ?></p>
                    </div>
                    
                    <div class="register-body">
                        <?php if (!empty($mesej_ralat)): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($mesej_kejayaan)): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="nokp" class="form-label">
                                        <i class="bi bi-card-text me-2"></i>No. Kad Pengenalan *
                                    </label>
                                    <input type="text" class="form-control" id="nokp" name="nokp"
                                           value="<?= htmlspecialchars($_POST['nokp'] ?? '') ?>"
                                           placeholder="Contoh: 123456789012" required>
                                    <small class="text-muted">Nombor sahaja tanpa tanda '-'</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="nama_penuh" class="form-label">
                                        <i class="bi bi-person-badge me-2"></i>Nama Penuh *
                                    </label>
                                    <input type="text" class="form-control" id="nama_penuh" name="nama_penuh"
                                           value="<?= htmlspecialchars($_POST['nama_penuh'] ?? '') ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="kata_laluan" class="form-label">
                                        <i class="bi bi-lock me-2"></i>Kata Laluan *
                                    </label>
                                    <input type="password" class="form-control" id="kata_laluan" name="kata_laluan" 
                                           minlength="6" required>
                                    <small class="text-muted">Minimum 6 aksara</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="sahkan_kata_laluan" class="form-label">
                                        <i class="bi bi-lock-fill me-2"></i>Sahkan Kata Laluan *
                                    </label>
                                    <input type="password" class="form-control" id="sahkan_kata_laluan" name="sahkan_kata_laluan" 
                                           minlength="6" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="emel" class="form-label">
                                        <i class="bi bi-envelope me-2"></i>Emel *
                                    </label>
                                    <input type="email" class="form-control" id="emel" name="emel" 
                                           value="<?= htmlspecialchars($_POST['emel'] ?? '') ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="no_telefon" class="form-label">
                                        <i class="bi bi-telephone me-2"></i>No. Telefon
                                    </label>
                                    <input type="tel" class="form-control" id="no_telefon" name="no_telefon" 
                                           value="<?= htmlspecialchars($_POST['no_telefon'] ?? '') ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="bahagian_id" class="form-label">
                                        <i class="bi bi-building me-2"></i>Bahagian *
                                    </label>
                                    <select class="form-select" id="bahagian_id" name="bahagian_id" required onchange="updateUnits()">
                                        <option value="">Pilih Bahagian</option>
                                        <?php foreach ($senarai_bahagian as $bahagian): ?>
                                            <option value="<?= $bahagian['id'] ?>"
                                                    <?= (($_POST['bahagian_id'] ?? '') == $bahagian['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($bahagian['bahagian']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="unit_id" class="form-label">
                                        <i class="bi bi-diagram-3 me-2"></i>Unit
                                    </label>
                                    <select class="form-select" id="unit_id" name="unit_id">
                                        <option value="">Pilih Unit</option>
                                        <?php foreach ($senarai_unit as $unit): ?>
                                            <option value="<?= $unit['id'] ?>" data-bahagian="<?= $unit['idbahagian'] ?>"
                                                    <?= (($_POST['unit_id'] ?? '') == $unit['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($unit['unit']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="jawatan_id" class="form-label">
                                        <i class="bi bi-briefcase me-2"></i>Jawatan *
                                    </label>
                                    <select class="form-select" id="jawatan_id" name="jawatan_id" required>
                                        <option value="">Pilih Jawatan</option>
                                        <?php foreach ($senarai_jawatan as $jawatan): ?>
                                            <option value="<?= $jawatan['id'] ?>"
                                                    <?= (($_POST['jawatan_id'] ?? '') == $jawatan['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($jawatan['jawatan']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="gred_id" class="form-label">
                                        <i class="bi bi-award me-2"></i>Gred *
                                    </label>
                                    <select class="form-select" id="gred_id" name="gred_id" required>
                                        <option value="">Pilih Gred</option>
                                        <?php foreach ($senarai_gred as $gred): ?>
                                            <option value="<?= $gred['id'] ?>"
                                                    <?= (($_POST['gred_id'] ?? '') == $gred['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($gred['gred']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-register">
                                    <i class="bi bi-person-plus me-2"></i>Daftar Akaun
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <p class="mb-0">Sudah mempunyai akaun?</p>
                            <a href="log_masuk.php" class="btn btn-outline-primary mt-2">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Log Masuk
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validasi kata laluan
        document.getElementById('sahkan_kata_laluan').addEventListener('input', function() {
            const password = document.getElementById('kata_laluan').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Kata laluan tidak sepadan');
            } else {
                this.setCustomValidity('');
            }
        });

        // Validasi No. KP
        document.getElementById('nokp').addEventListener('input', function() {
            const nokp = this.value.replace(/\D/g, ''); // Buang semua bukan digit
            this.value = nokp;

            if (nokp.length === 0) {
                this.setCustomValidity('No. KP diperlukan');
            } else {
                this.setCustomValidity('');
            }
        });

        // Function untuk update unit berdasarkan bahagian
        function updateUnits() {
            const bahagianId = document.getElementById('bahagian_id').value;
            const unitSelect = document.getElementById('unit_id');
            const allOptions = unitSelect.querySelectorAll('option[data-bahagian]');

            // Reset unit selection
            unitSelect.value = '';

            // Show/hide options based on selected bahagian
            allOptions.forEach(option => {
                if (bahagianId === '' || option.dataset.bahagian === bahagianId) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        }

        // Initialize unit filter on page load
        updateUnits();
    </script>
</body>
</html>
